import React from "react";
import { Room, Booking } from "../types";
import BookingBlock from "./BookingBlock";
import { getDateBlockWidth, getCenteredBookingPosition } from "./timeUtils";

interface RoomRowProps {
  room: Room;
  bookings: Booking[];
  availableBlocks?: Array<{
    id: string;
    room_id: string;
    date: Date;
    status: string;
  }>;
  dateSlots: Date[];
  startDate: Date;
  endDate: Date;
  timelineWidth: number;
  isHighlighted?: boolean;
  onHover?: (roomId: string | null) => void;
  hasStatusFilter?: boolean;
  isAvailableFilterActive?: boolean;
  onViewBooking?: (bookingId: string) => void;
  onUpdateStatus?: (booking: Booking) => void;
  onMoveToUnallocated?: (booking: Booking, room: Room) => void;
  // TODO: Re-enable split booking functionality in the future
  // onSplitBooking?: (booking: Booking, room: Room) => void;
  onBookingClick?: (booking: Booking) => void;
  onMarkAsMaintenance?: (booking: Booking) => void;
  onMarkAsAvailable?: (booking: Booking) => void;
  selectedBookingId?: string | null;
  hasEditPermission?: boolean;
}

const RoomRow: React.FC<RoomRowProps> = ({
  room,
  bookings,
  availableBlocks = [],
  dateSlots,
  startDate,
  endDate,
  timelineWidth,
  isHighlighted = false,
  onHover,
  hasStatusFilter = false,
  isAvailableFilterActive = false,
  onViewBooking,
  onUpdateStatus,
  onMoveToUnallocated,
  // TODO: Re-enable split booking functionality in the future
  // onSplitBooking,
  onBookingClick,
  onMarkAsMaintenance,
  onMarkAsAvailable,
  selectedBookingId,
  hasEditPermission = true,
}) => {
  const ROOM_HEIGHT = 80; // Full room height

  return (
    <div
      className={`relative h-20 border-b border-border transition-all duration-200 ${
        isHighlighted
          ? "bg-primary/10 border-l-4 border-l-primary shadow-sm"
          : "hover:bg-muted/30"
      }`}
      onMouseEnter={() => onHover?.(room.id)}
      onMouseLeave={() => onHover?.(null)}
    >
      {/* Background grid with proper alignment */}
      <div className="absolute inset-0 flex">
        {dateSlots.map((_, index) => (
          <div
            key={index}
            className={`border-r transition-colors ${
              isHighlighted ? "border-primary/20" : "border-border/50"
            }`}
            style={{ width: "150px", height: "100%" }}
          />
        ))}
      </div>

      {/* Available background with proper dimensions - show when no status filter OR when available filter is active */}
      {(!hasStatusFilter || isAvailableFilterActive) && (
        <div
          className={`absolute inset-0 rounded-sm opacity-20 transition-colors ${
            isHighlighted ? "bg-blue-100" : "bg-green-100"
          }`}
          style={{ width: `${timelineWidth}px`, height: "100%" }}
        />
      )}

      {/* Highlight indicator with proper positioning */}
      {isHighlighted && (
        <div className="absolute right-6 top-1/2 transform -translate-y-1/2 z-20">
          <div className="w-3 h-3 bg-primary rounded-full animate-pulse shadow-lg" />
        </div>
      )}

      {/* Booking blocks with precise alignment */}
      {bookings.map((booking) => {
        const left = getCenteredBookingPosition(
          booking.checkIn,
          booking.checkOut,
          startDate,
          endDate,
          timelineWidth
        );
        const width = getDateBlockWidth(
          booking.checkIn,
          booking.checkOut,
          startDate,
          endDate,
          timelineWidth
        );

        return (
          <BookingBlock
            key={booking.id}
            booking={booking}
            left={left}
            width={width}
            height={ROOM_HEIGHT - 8} // Full height minus padding for proper spacing
            room={room}
            isHighlighted={isHighlighted}
            isSelected={selectedBookingId === (booking.order_id || booking.id)}
            onViewBooking={onViewBooking}
            onUpdateStatus={onUpdateStatus}
            onMoveToUnallocated={onMoveToUnallocated}
            // TODO: Re-enable split booking functionality in the future
            // onSplitBooking={onSplitBooking}
            onClick={onBookingClick}
            onMarkAsMaintenance={onMarkAsMaintenance}
            onMarkAsAvailable={onMarkAsAvailable}
            hasEditPermission={hasEditPermission}
          />
        );
      })}

      {/* Available blocks - render green blocks for available dates */}
      {availableBlocks
        .filter((block) => block.room_id === room.id)
        .map((block) => {
          const dayWidth = timelineWidth / dateSlots.length;
          const dayIndex = dateSlots.findIndex(
            (date) =>
              date.toISOString().split("T")[0] ===
              block.date.toISOString().split("T")[0]
          );

          if (dayIndex === -1) return null;

          const left = dayIndex * dayWidth;

          return (
            <div
              key={block.id}
              className="absolute bg-green-100 border border-green-300 rounded-sm opacity-80 z-10"
              style={{
                left: `${left}px`,
                width: `${dayWidth - 2}px`, // Slight gap between blocks
                height: `${ROOM_HEIGHT - 8}px`,
                top: "4px",
              }}
            >
              <div className="flex items-center justify-center h-full">
                <span className="text-xs font-medium text-green-800">
                  Available
                </span>
              </div>
            </div>
          );
        })}

      {/* Empty state message with proper centering */}
      {bookings.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span
            className={`text-sm font-medium transition-colors ${
              isHighlighted ? "text-primary" : "text-muted-foreground"
            }`}
          >
            Available
          </span>
        </div>
      )}
    </div>
  );
};

export default RoomRow;
