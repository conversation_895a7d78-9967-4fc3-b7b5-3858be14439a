import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Select,
  DatePicker,
  Badge,
  Text,
  toast,
} from "@camped-ai/ui";
import { Check, X, Loader2 } from "lucide-react";
import { CircularProgress } from "@mui/material";

interface BaseInlineEditCellProps {
  value: any;
  onSave: (value: any) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

interface InlineEditNumberCellProps extends BaseInlineEditCellProps {
  type: "number";
  value: number | null;
  currency?: string;
  placeholder?: string;
  min?: number;
  max?: number;
}

interface InlineEditSelectCellProps extends BaseInlineEditCellProps {
  type: "select";
  value: string;
  options: Array<{ value: string; label: string; variant?: string }>;
}

interface InlineEditDateCellProps extends BaseInlineEditCellProps {
  type: "date";
  value: Date | string | null;
  placeholder?: string;
}

interface InlineEditTextCellProps extends BaseInlineEditCellProps {
  type: "text";
  value: string;
  placeholder?: string;
}

type InlineEditCellProps =
  | InlineEditNumberCellProps
  | InlineEditSelectCellProps
  | InlineEditDateCellProps
  | InlineEditTextCellProps;

const InlineEditCell: React.FC<InlineEditCellProps> = (props) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(props.value);
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const selectRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    setEditValue(props.value);
  }, [props.value]);

  useEffect(() => {
    if (isEditing) {
      // Focus the input when entering edit mode
      setTimeout(() => {
        if ((props.type === "number" || props.type === "text") && inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();
        } else if (props.type === "select" && selectRef.current) {
          selectRef.current.focus();
        }
      }, 0);
    }
  }, [isEditing, props.type]);

  const handleSave = async () => {
    if (editValue === props.value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    try {
      await props.onSave(editValue);
      setIsEditing(false);
      // Don't show success toast here - let the parent component handle it
      // This prevents duplicate success messages
    } catch (error: any) {
      // Handle specific error types for better user experience
      let errorMessage = "Failed to update";

      if (error?.response?.status === 409) {
        errorMessage = error?.response?.data?.message || error.message || "Conflict with existing data";
      } else if (error?.response?.status === 400) {
        errorMessage = error?.response?.data?.message || error.message || "Invalid data provided";
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      setEditValue(props.value); // Reset to original value
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(props.value);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    } else if (e.key === "Tab") {
      // Allow Tab to save and move to next field
      handleSave();
    }
  };

  const handleClickOutside = (e: React.FocusEvent) => {
    // Only save if we're not clicking on the save/cancel buttons
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      handleSave();
    }
  };

  if (!isEditing) {
    return (
      <div
        className={`cursor-pointer hover:bg-ui-bg-subtle rounded px-2 py-1 transition-colors border border-transparent hover:border-ui-border-base ${props.isLoading ? 'opacity-50' : ''} ${props.className || ""}`}
        onClick={() => !props.isLoading && setIsEditing(true)}
        title={props.isLoading ? "Updating..." : "Click to edit"}
      >
        {props.type === "number" && (
          <div className="flex items-center gap-2">
            <div>
              <div className="font-medium">
                {props.value ? `${props.value} ${(props as InlineEditNumberCellProps).currency || 'CHF'}` : "Not set"}
              </div>
              <div className="text-xs text-ui-fg-subtle">
                {props.isLoading ? "Updating..." : "Click to edit"}
              </div>
            </div>
            {props.isLoading && <CircularProgress size={12} />}
          </div>
        )}
        
        {props.type === "select" && (
          <div className="flex items-center gap-2">
            <Badge variant={getStatusBadgeVariant(props.value) as any}>
              {props.value}
            </Badge>
            {props.isLoading && <Loader2 className="h-3 w-3 animate-spin text-ui-fg-subtle" />}
          </div>
        )}

        {props.type === "date" && (
          <div className="flex items-center gap-2">
            <Text size="small">
              {props.value ? formatDate(props.value) : "Not set"}
            </Text>
            {props.isLoading && <Loader2 className="h-3 w-3 animate-spin text-ui-fg-subtle" />}
          </div>
        )}

        {props.type === "text" && (
          <div className="flex items-center gap-2">
            <Text size="small">
              {props.value || (props as InlineEditTextCellProps).placeholder || "Click to add..."}
            </Text>
            {props.isLoading && <Loader2 className="h-3 w-3 animate-spin text-ui-fg-subtle" />}
          </div>
        )}
      </div>
    );
  }

  return (
    <div 
      className={`relative ${props.className || ""}`}
      onBlur={handleClickOutside}
    >
      {props.type === "number" && (
        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            type="number"
            value={editValue || ""}
            onChange={(e) => setEditValue(e.target.value ? parseFloat(e.target.value) : null)}
            onKeyDown={handleKeyDown}
            placeholder={(props as InlineEditNumberCellProps).placeholder}
            min={(props as InlineEditNumberCellProps).min}
            max={(props as InlineEditNumberCellProps).max}
            className="w-24"
            disabled={isSaving}
          />
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="p-1 text-green-600 hover:bg-green-50 rounded"
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="p-1 text-red-600 hover:bg-red-50 rounded"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}

      {props.type === "text" && (
        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            type="text"
            value={editValue || ""}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={(props as InlineEditTextCellProps).placeholder}
            className="min-w-32"
            disabled={isSaving}
          />
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="p-1 text-green-600 hover:bg-green-50 rounded"
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="p-1 text-red-600 hover:bg-red-50 rounded"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}

      {props.type === "select" && (
        <div className="flex items-center gap-2">
          <Select
            value={editValue}
            onValueChange={setEditValue}
            disabled={isSaving}
          >
            <Select.Trigger ref={selectRef} className="w-32">
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {(props as InlineEditSelectCellProps).options.map((option) => (
                <Select.Item key={option.value} value={option.value}>
                  {option.label}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="p-1 text-green-600 hover:bg-green-50 rounded"
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="p-1 text-red-600 hover:bg-red-50 rounded"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}

      {props.type === "date" && (
        <div className="flex items-center gap-2">
          <DatePicker
            value={editValue ? new Date(editValue) : null}
            onChange={(date) => setEditValue(date)}
            placeholder={(props as InlineEditDateCellProps).placeholder}
            disabled={isSaving}
          />
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="p-1 text-green-600 hover:bg-green-50 rounded"
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="p-1 text-red-600 hover:bg-red-50 rounded"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case "active":
      return "green";
    case "inactive":
      return "red";
    default:
      return "grey";
  }
};

const formatDate = (date: Date | string | null) => {
  if (!date) return "Not set";
  const d = new Date(date);
  return d.toLocaleDateString();
};

export default InlineEditCell;
