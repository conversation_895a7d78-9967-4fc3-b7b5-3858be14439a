import React from "react";
import { Button } from "@camped-ai/ui";
import { Table, Grid } from "lucide-react";

export type ViewMode = "table" | "card";

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  return (
    <div className={`flex border border-ui-border-base rounded-md overflow-hidden shadow-sm ${className}`}>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewModeChange("table")}
        className={`rounded-none border-0 ${
          viewMode === "table"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
      >
        <Table className="w-4 h-4" />
      </Button>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewModeChange("card")}
        className={`rounded-none border-0 ${
          viewMode === "card"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
      >
        <Grid className="w-4 h-4" />
      </Button>
    </div>
  );
};
