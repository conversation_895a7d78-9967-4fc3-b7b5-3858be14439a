import { Container, Heading, Text, Badge } from "@camped-ai/ui";
import { Hotel, Calendar, CreditCard, Clock } from "lucide-react";
import { format } from "date-fns";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { EnhancedConciergeOrder, PaymentSummary } from "../types";

interface BookingDetailsSectionProps {
  booking: EnhancedConciergeOrder | null;
}

const BookingDetailsSection = ({ booking }: BookingDetailsSectionProps) => {
  if (!booking) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Booking Details</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-ui-fg-muted">Loading booking information...</Text>
        </div>
      </Container>
    );
  }
  // Format date helper
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not specified";
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format currency helper
  const formatCurrency = (amount: number, currencyCode: string = 'CHF') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount / 100); // Assuming amount is in cents
  };

  // Calculate payment summary
  const getPaymentSummary = (): PaymentSummary => {
    const totalAmount = booking.order_total || booking.order?.total || 0;
    
    let totalPaid = 0;
    const payments = booking.payment_collections.map(pc => {
      const amount = pc.captured_amount || pc.amount || 0;
      totalPaid += amount;
      
      return {
        amount,
        date: pc.captured_at || pc.created_at,
        status: pc.status,
        payment_method: pc.metadata?.payment_method || 'Card'
      };
    });

    return {
      total_paid: totalPaid,
      remaining_balance: totalAmount - totalPaid,
      payments
    };
  };

  // Get hotel information
  const getHotelInfo = () => {
    return {
      name: booking.hotel_name || 
            booking.order?.metadata?.hotel_name || 
            booking.order_line_items?.[0]?.metadata?.hotel_name ||
            booking.hotel_id ||
            "Hotel information not available",
      id: booking.hotel_id || booking.order?.metadata?.hotel_id
    };
  };

  const hotelInfo = getHotelInfo();
  const paymentSummary = getPaymentSummary();
  const currency = booking.order_currency_code || booking.order?.currency_code || 'CHF';

  // Get payment status color
  const getPaymentStatusColor = () => {
    if (paymentSummary.remaining_balance <= 0) return "green";
    if (paymentSummary.total_paid > 0) return "orange";
    return "red";
  };

  const getPaymentStatusText = () => {
    if (paymentSummary.remaining_balance <= 0) return "Fully Paid";
    if (paymentSummary.total_paid > 0) return "Partially Paid";
    return "Unpaid";
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Booking Details</Heading>
        <Badge variant={getPaymentStatusColor() as any}>
          <CreditCard className="h-3 w-3 mr-1" />
          {getPaymentStatusText()}
        </Badge>
      </div>

      <div className="px-6 py-4 space-y-4">
        {/* Hotel Information */}
        <SectionRow title="Hotel" value={hotelInfo.name}>
          <div className="flex items-center gap-2">
            <Hotel className="h-4 w-4 text-ui-fg-muted" />
            <Text>{hotelInfo.name}</Text>
            {hotelInfo.id && hotelInfo.id !== hotelInfo.name && (
              <Text className="text-ui-fg-muted text-sm">({hotelInfo.id})</Text>
            )}
          </div>
        </SectionRow>

        {/* Check-in Date */}
        <SectionRow title="Check-in Date" value={formatDate(booking.check_in_date)}>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-ui-fg-muted" />
            <Text>{formatDate(booking.check_in_date)}</Text>
          </div>
        </SectionRow>

        {/* Check-out Date */}
        <SectionRow title="Check-out Date" value={formatDate(booking.check_out_date)}>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-ui-fg-muted" />
            <Text>{formatDate(booking.check_out_date)}</Text>
          </div>
        </SectionRow>

        {/* Total Booking Amount */}
        <SectionRow 
          title="Total Booking Amount" 
          value={formatCurrency(booking.order_total || 0, currency)}
        >
          <div className="flex items-center gap-2">
            <CreditCard className="h-4 w-4 text-ui-fg-muted" />
            <Text className="font-medium">
              {formatCurrency(booking.order_total || 0, currency)}
            </Text>
          </div>
        </SectionRow>
      </div>

      {/* Payment Breakdown Sub-section */}
      <div className="px-6 py-4">
        <Heading level="h3" className="mb-4">Payment Breakdown</Heading>
        
        <div className="space-y-3">
          {/* Payment Summary */}
          <div className="grid grid-cols-2 gap-4 p-3 bg-ui-bg-subtle rounded-lg">
            <div>
              <Text className="text-sm text-ui-fg-muted">Total Paid</Text>
              <Text className="font-medium text-green-600">
                {formatCurrency(paymentSummary.total_paid, currency)}
              </Text>
            </div>
            <div>
              <Text className="text-sm text-ui-fg-muted">Remaining Balance</Text>
              <Text className={`font-medium ${paymentSummary.remaining_balance > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                {formatCurrency(paymentSummary.remaining_balance, currency)}
              </Text>
            </div>
          </div>

          {/* Individual Payments */}
          {paymentSummary.payments.length > 0 ? (
            <div className="space-y-2">
              <Text className="text-sm font-medium text-ui-fg-muted">Payment History</Text>
              {paymentSummary.payments.map((payment, index) => (
                <div key={index} className="flex items-center justify-between p-2 border border-ui-border-base rounded">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-ui-fg-muted" />
                    <Text className="text-sm">
                      {formatDate(payment.date)} - {payment.payment_method}
                    </Text>
                    <Badge variant={payment.status === 'captured' ? 'green' : 'orange'} size="small">
                      {payment.status}
                    </Badge>
                  </div>
                  <Text className="font-medium">
                    {formatCurrency(payment.amount, currency)}
                  </Text>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-ui-fg-muted">
              <Text>No payments recorded</Text>
            </div>
          )}
        </div>
      </div>
    </Container>
  );
};

export default BookingDetailsSection;
