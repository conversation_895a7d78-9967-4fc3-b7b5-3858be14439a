import { use<PERSON>emo, use<PERSON>allback, useState } from "react";
import { Trash } from "@camped-ai/icons";
import {
  <PERSON><PERSON>,
  Container,
  createDataTableColumnHelper,
  DataTableAction,
  Heading,
  usePrompt,
  Badge,
  Text,
} from "@camped-ai/ui";
import { CellContext } from "@tanstack/react-table";

import { DataTable } from "../../../../../../components/table/data-table";
import { useDataTable } from "../../../../../../hooks/use-data-table";
import { useRbac } from "../../../../../../admin/hooks/use-rbac";
import { InlineAddOnForm } from "../../../../../../admin/components/booking/add-on-management/InlineAddOnForm";
import { useUpdateBookingAddOn, useDeleteBookingAddOn, useUpdateConciergeOrderItem, useDeleteConciergeOrderItem } from "../../../../../../admin/hooks/concierge-management/use-booking-add-ons";
import InlineEditCell from "../../../../../../admin/components/supplier-management/inline-edit-cell";
import StatusChangeCell from "../../../../../../admin/components/concierge-management/status-change-cell";
import NotesEditCell from "../../../../../../admin/components/concierge-management/notes-edit-cell";
import { Plus, Package } from "lucide-react";

type AddOnItem = {
  id: string;
  concierge_order_id: string;
  line_item_id: string | null;
  variant_id: string;
  quantity: number;
  unit_price: number;
  title: string;
  status: string;
  notes: string | null;
  is_active: boolean;
  added_by: string | null;
  finalized_by: string | null;
  added_at: string;
  finalized_at: string | null;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  // Backward compatibility fields
  add_on_name?: string;
  add_on_variant_id?: string;
  total_price?: number;
  currency_code?: string;
  customer_field_responses?: Record<string, any>;
  add_on_metadata?: Record<string, any>;
  order_status?: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
};

interface ConciergeBookingAddOnsTableProps {
  booking: any;
  addOns: AddOnItem[];
  onAddOnAdded?: () => void;
}

const PAGE_SIZE = 10;

export const ConciergeBookingAddOnsTable = ({
  booking,
  addOns,
  onAddOnAdded
}: ConciergeBookingAddOnsTableProps) => {
  const { hasPermission } = useRbac();
  const [showAddOnForm, setShowAddOnForm] = useState(false);

  const columns = useColumns(onAddOnAdded, booking?.id);

  // Create table instance
  const { table } = useDataTable({
    data: addOns || [],
    columns,
    count: addOns?.length || 0,
    pageSize: PAGE_SIZE,
    getRowId: (row) => row.id,
  });

  const handleAddOnSuccess = () => {
    setShowAddOnForm(false); // Hide form after successful addition
    if (onAddOnAdded) {
      onAddOnAdded();
    }
  };

  // Calculate total amount
  const totalAmount = useMemo(() => {
    return addOns?.reduce((sum, addon) => {
      const itemTotal = addon.total_price || (addon.unit_price * addon.quantity);
      return sum + itemTotal;
    }, 0) || 0;
  }, [addOns]);

  // Get currency from first addon or default to CHF
  const currency = useMemo(() => {
    return addOns?.[0]?.metadata?.currency_code ||
           addOns?.[0]?.currency_code ||
           booking?.currency_code ||
           'GBP';
  }, [addOns, booking]);

  // Format currency helper
  const formatCurrency = useCallback((amount: number, currencyCode: string = 'CHF') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  }, []);



  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <Heading>Add-ons & Services</Heading>
          <Badge color="grey" size="small">
            <Package className="h-3 w-3 mr-1" />
            {addOns?.length || 0} Item{(addOns?.length || 0) !== 1 ? 's' : ''}
          </Badge>
        </div>
        {hasPermission("concierge_management:update") && (
          <Button
            onClick={() => setShowAddOnForm(true)}
            size="small"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add-ons & Services
          </Button>
        )}
      </div>

      {/* Inline Add-on Form */}
      {hasPermission("concierge_management:update") && showAddOnForm && (
        <div className="px-6 py-4 border-b">
          <InlineAddOnForm
            bookingId={booking?.id}
            checkInDate={booking?.check_in_date}
            checkOutDate={booking?.check_out_date}
            onAddOnAdded={handleAddOnSuccess}
            onCancel={() => setShowAddOnForm(false)}
            existingAddOnIds={addOns?.map(addon => addon.add_on_metadata?.product_service_id).filter(Boolean) || []}
          />
        </div>
      )}

      <DataTable
        table={table}
        columns={columns}
        pageSize={PAGE_SIZE}
        count={addOns?.length || 0}
        isLoading={false}
        pagination={false}
        queryObject={{}}
        noRecords={{
          title: "No add-ons",
          message: "No add-ons have been booked for this reservation.",
        }}
      />

      {/* Total Summary - Only show if there are add-ons */}
      {addOns && addOns.length > 0 && (
        <div className="px-6 py-4">
          <div className="flex justify-end">
            <div className="bg-ui-bg-subtle p-4 rounded-lg">
              <div className="flex items-center justify-between gap-8">
                <Text className="font-medium">Total Add-ons & Services:</Text>
                <Text className="font-bold text-lg">
                  {formatCurrency(totalAmount, currency)}
                </Text>
              </div>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

const columnHelper = createDataTableColumnHelper<AddOnItem>();

const useColumns = (onAddOnAdded?: () => void, conciergeOrderId?: string) => {
  const prompt = usePrompt();
  const updateBookingAddOnMutation = useUpdateBookingAddOn();
  const deleteBookingAddOnMutation = useDeleteBookingAddOn();
  const updateConciergeOrderItemMutation = useUpdateConciergeOrderItem();
  const deleteConciergeOrderItemMutation = useDeleteConciergeOrderItem();
  const { hasPermission } = useRbac();

  const formatPrice = useCallback((price: number, currencyCode: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(price || 0);
  }, []);

  const formatCustomerResponses = useCallback((responses: any) => {
    if (!responses || Object.keys(responses).length === 0) {
      return "No additional details";
    }

    const formattedResponses = Object.entries(responses)
      .filter(([, value]) => value !== null && value !== undefined && value !== "")
      .map(([key, value]) => {
        const readableKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        return `${readableKey}: ${String(value)}`;
      })
      .join(' • ');

    return formattedResponses || "No additional details";
  }, []);

  const handleDelete = useCallback(
    async (id: string, name: string) => {
      const res = await prompt({
        title: "Are you sure?",
        description: `This will remove "${name}" from the booking. This action cannot be undone.`,
        confirmText: "Remove",
        cancelText: "Cancel",
      });

      if (!res) {
        return;
      }

      try {
        if (conciergeOrderId) {
          // Use concierge order item deletion
          await deleteConciergeOrderItemMutation.mutateAsync({
            conciergeOrderId,
            itemId: id,
          });
        } else {
          // Fallback to booking add-on deletion
          await deleteBookingAddOnMutation.mutateAsync(id);
        }
        if (onAddOnAdded) {
          onAddOnAdded(); // Refresh the data
        }
      } catch (error) {
        console.error("Error deleting add-on:", error);
      }
    },
    [prompt, deleteBookingAddOnMutation, deleteConciergeOrderItemMutation, conciergeOrderId, onAddOnAdded]
  );

  // Handle inline field updates
  const handleFieldUpdate = useCallback(
    async (id: string, field: string, value: any) => {
      try {
        if (conciergeOrderId) {
          // Use concierge order item update
          await updateConciergeOrderItemMutation.mutateAsync({
            conciergeOrderId,
            itemId: id,
            [field]: value,
          });
        } else {
          // Fallback to booking add-on update
          await updateBookingAddOnMutation.mutateAsync({
            id,
            [field]: value,
          });
        }
        if (onAddOnAdded) {
          onAddOnAdded(); // Refresh the data
        }
      } catch (error) {
        console.error(`Error updating ${field}:`, error);
        throw error; // Re-throw to let the inline edit component handle it
      }
    },
    [updateBookingAddOnMutation, updateConciergeOrderItemMutation, conciergeOrderId, onAddOnAdded]
  );

  // Helper function to get status badge variant
  const getStatusBadgeVariant = useCallback((status: string) => {
    switch (status?.toLowerCase()) {
      case "confirmed":
      case "active":
      case "client_confirmed":
        return "green";
      case "pending":
      case "under_review":
        return "orange";
      case "cancelled":
      case "inactive":
        return "red";
      case "in_progress":
        return "blue";
      case "completed":
        return "green";
      case "order_placed":
        return "purple";
      default:
        return "grey";
    }
  }, []);

  // Helper function to format status text
  const formatStatusText = useCallback((status: string) => {
    return status?.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) || "Unknown";
  }, []);

  const getActions = useCallback(
    (ctx: CellContext<AddOnItem, unknown>) => {
      const addon = ctx.row.original;

      // Only show actions if user has delete permission
      if (!hasPermission("concierge_management:delete")) {
        return [[], []]; // No actions for view-only users
      }

      const actions: DataTableAction<AddOnItem>[] = [
        {
          icon: <Trash />,
          label: "Remove",
          onClick: () => handleDelete(addon.id, addon.title || addon.add_on_name || "Service"),
        },
      ];

      return [[], actions]; // [mainActions, secondaryActions]
    },
    [handleDelete, hasPermission]
  );

  return useMemo(() => {
    return [
      columnHelper.display({
        id: "service_name",
        header: "Service",
        cell: ({ row }) => {
          const addon = row.original;

          // Try to get a better service name from various sources
          let serviceName = addon.title || addon.add_on_name || "Unknown Service";

          // If the title is generic "Product Item", try to get a better name from related data
          if (serviceName === "Product Item") {
            let foundBetterName = false;

            // Helper function to extract meaningful name from metadata
            const extractNameFromMetadata = (metadata: any) => {
              if (!metadata) return null;

              // Priority order for name extraction
              return metadata.room_config_name ||
                     metadata.room_name ||
                     metadata.product_name ||
                     metadata.service_name ||
                     metadata.variant_title ||
                     metadata.title ||
                     null;
            };

            // Try to get name from order line item metadata first (most reliable)
            if (addon.order_line_item?.metadata && !foundBetterName) {
              const betterName = extractNameFromMetadata(addon.order_line_item.metadata);
              if (betterName) {
                serviceName = betterName;
                foundBetterName = true;
              }
            }

            // Try to get name from order item metadata
            if (addon.order_item?.metadata && !foundBetterName) {
              const betterName = extractNameFromMetadata(addon.order_item.metadata);
              if (betterName) {
                serviceName = betterName;
                foundBetterName = true;
              }
            }

            // Try to get name from add-on metadata
            if (addon.add_on_metadata && !foundBetterName) {
              const betterName = extractNameFromMetadata(addon.add_on_metadata);
              if (betterName) {
                serviceName = betterName;
                foundBetterName = true;
              }
            }

            // If still no better name found, try to construct one from hotel + room info
            if (!foundBetterName) {
              const metadata = addon.order_line_item?.metadata || addon.order_item?.metadata || addon.add_on_metadata;
              if (metadata) {
                const hotelName = metadata.hotel_name;
                const roomType = metadata.room_config_name || metadata.room_name;

                if (roomType) {
                  serviceName = roomType;
                } else if (hotelName) {
                  serviceName = `${hotelName} - Room`;
                }
              }
            }
          }

          return (
            <div className="w-[200px]">
              <div className="font-medium text-ui-fg-base leading-tight">
                {serviceName}
              </div>
            </div>
          );
        },
      }),
      columnHelper.accessor("quantity", {
        header: "Qty",
        cell: ({ row }) => {
          const addon = row.original;

          // Only allow editing if user has concierge_management:edit permission
          if (!hasPermission("concierge_management:edit")) {
            return (
              <div className="text-ui-fg-base">
                {addon.quantity || 1}
              </div>
            );
          }

          return (
            <InlineEditCell
              type="text"
              value={String(addon.quantity || 1)}
              onSave={(value) => handleFieldUpdate(addon.id, "quantity", parseInt(value) || 1)}
              placeholder="1"
              isLoading={conciergeOrderId ? updateConciergeOrderItemMutation.isPending : updateBookingAddOnMutation.isPending}
            />
          );
        },
      }),
      columnHelper.accessor("unit_price", {
        header: "Unit Price",
        cell: ({ row }) => {
          const addon = row.original;
          // Get currency from metadata or default to USD
          const currencyCode = addon.metadata?.currency_code || addon.currency_code || "USD";

          // Only allow editing if user has concierge_management:edit permission
          if (!hasPermission("concierge_management:edit")) {
            const formatPrice = (price: number, currencyCode: string = "USD") => {
              return new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currencyCode,
              }).format(price);
            };

            return (
              <div className="text-ui-fg-base">
                {formatPrice(addon.unit_price || 0, currencyCode)}
              </div>
            );
          }

          return (
            <InlineEditCell
              type="number"
              value={addon.unit_price || 0}
              onSave={(value) => handleFieldUpdate(addon.id, "unit_price", value)}
              currency={currencyCode}
              min={0}
              isLoading={conciergeOrderId ? updateConciergeOrderItemMutation.isPending : updateBookingAddOnMutation.isPending}
            />
          );
        },
      }),
      columnHelper.accessor("status", {
        header: "Status",
        cell: ({ row }) => {
          const addon = row.original;

          // Only allow editing if user has concierge_management:edit permission
          if (!hasPermission("concierge_management:edit")) {
            return (
              <Badge variant={getStatusBadgeVariant(addon.status) as any}>
                {formatStatusText(addon.status)}
              </Badge>
            );
          }

          return (
            <StatusChangeCell
              value={addon.status}
              onSave={(status) => handleFieldUpdate(addon.id, "status", status)}
              isLoading={conciergeOrderId ? updateConciergeOrderItemMutation.isPending : updateBookingAddOnMutation.isPending}
            />
          );
        },
      }),
      columnHelper.accessor("notes", {
        header: "Notes",
        cell: ({ row }) => {
          const addon = row.original;

          // Only allow editing if user has concierge_management:edit permission
          if (!hasPermission("concierge_management:edit")) {
            return (
              <div className="text-ui-fg-base text-sm">
                {addon.notes || "-"}
              </div>
            );
          }

          return (
            <NotesEditCell
              value={addon.notes}
              onSave={(notes) => handleFieldUpdate(addon.id, "notes", notes)}
              isLoading={conciergeOrderId ? updateConciergeOrderItemMutation.isPending : updateBookingAddOnMutation.isPending}
              placeholder="Add notes for this item..."
            />
          );
        },
      }),
      columnHelper.display({
        id: "total_price",
        header: "Total",
        cell: ({ row }) => {
          const addon = row.original;
          const totalPrice = addon.total_price || (addon.unit_price * addon.quantity);
          const currency = addon.metadata?.currency_code || addon.currency_code || "USD";
          return (
            <div className="font-semibold text-ui-fg-base">
              {formatPrice(totalPrice, currency)}
            </div>
          );
        },
      }),
      columnHelper.action({
        actions: getActions,
      }),
    ];
  }, [formatPrice, formatCustomerResponses, getActions, handleFieldUpdate, updateBookingAddOnMutation.isPending, updateConciergeOrderItemMutation.isPending, conciergeOrderId, getStatusBadgeVariant, formatStatusText, hasPermission]);
};

export default ConciergeBookingAddOnsTable;
