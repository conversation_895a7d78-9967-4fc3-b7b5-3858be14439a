import { useState, useEffect } from "react";
import {
  But<PERSON>,
  Heading,
  Text,
  Toaster,
  toast,
  Badge,
  Container,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft } from "lucide-react";

import { TwoColumnPage } from "../../../../../components/layout/pages";
import { useRbac } from "../../../../hooks/use-rbac";
import Spinner from "../../../../components/shared/spinner";

// Import section components
import ConciergeBookingItinerarySection from "./components/concierge-booking-itinerary-section.tsx";
import ConciergeBookingTabsSection from "./components/concierge-booking-tabs-section";
import ConciergeBookingAddOnsTable from "./components/concierge-booking-addons-table";
import ConciergeBookingCustomerSection from "./components/concierge-booking-customer-section";

import BookingDetailsSection from "./components/booking-details-section";
import ConciergeManagementActions from "./components/concierge-management-actions";
import { EnhancedConciergeOrder } from "./types";

const ConciergeBookingDetailPageClient = () => {
  const { id: bookingId } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [conciergeOrder, setConciergeOrder] =
    useState<EnhancedConciergeOrder | null>(null);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // State for managing the assigned value from ConciergeManagementActions
  const [currentAssignedTo, setCurrentAssignedTo] = useState<string>("");

  // Fetch concierge order details
  const fetchBookingDetails = async () => {
    if (!bookingId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/concierge-management/orders/${bookingId}`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch concierge order details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.concierge_order) {
        throw new Error("No concierge order data in response");
      }

      setConciergeOrder(data.concierge_order);

      // Initialize the assigned value from the concierge order
      setCurrentAssignedTo(data.concierge_order.assigned_to || "");

      // Fetch hotel details if we have a hotel ID in the order metadata
      const hotelId = data.concierge_order.order?.metadata?.hotel_id;
      if (hotelId) {
        fetchHotelDetails(hotelId);
      }
    } catch (error: any) {
      console.error("Error fetching concierge order details:", error);
      toast.error("Error", {
        description: `Failed to fetch concierge order details: ${error.message}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch hotel details
  const fetchHotelDetails = async (hotelId: string) => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.hotel) {
          setHotelDetails(data.hotel);
        }
      }
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchBookingDetails();
  }, [bookingId]);

  // Go back to bookings list
  const handleGoBack = () => {
    navigate("/concierge-management/bookings");
  };

  if (isLoading) {
    return (
      <Container>
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4 text-muted-foreground">
            Loading concierge order details...
          </div>
        </div>
      </Container>
    );
  }

  if (!conciergeOrder) {
    return (
      <Container>
        <div className="text-center py-8">
          <Heading>Concierge Order Not Found</Heading>
          <Text className="mt-2">
            The concierge order you're looking for doesn't exist or has been
            removed.
          </Text>
          <Button className="mt-4" onClick={handleGoBack}>
            Back to Bookings
          </Button>
        </div>
      </Container>
    );
  }

  // Helper function to extract hotel stay information from order items
  const extractStayDetailsFromOrderItems = () => {
    const orderItems = conciergeOrder.order?.items || [];
    if (orderItems.length === 0) return {};

    // Get the first item (assuming it contains the hotel booking information)
    const firstItem = orderItems[0];
    const itemMetadata = firstItem?.metadata || {};

    // Calculate total number of rooms based on line items
    // Each line item typically represents one room booking
    const totalRooms = orderItems.length;

    // Calculate total guests - sum up quantities or use metadata
    const totalGuests = orderItems.reduce((total: number, item: any) => {
      const guestsFromMetadata = item?.metadata?.number_of_guests || 1;
      const quantity = item?.quantity || 1;
      // Use the higher of metadata guests or quantity, but typically quantity for room bookings
      const itemGuests = Math.max(guestsFromMetadata, quantity);
      return total + itemGuests;
    }, 0);

    // Extract room type information - try to get the most descriptive room name
    const roomType =
      itemMetadata.room_config_name ||
      itemMetadata.room_name ||
      itemMetadata.room_type ||
      "Room";

    return {
      hotel_id: itemMetadata.hotel_id,
      hotel_name: itemMetadata.hotel_name,
      room_config_name: itemMetadata.room_config_name,
      room_type: roomType,
      check_in_date: itemMetadata.check_in_date,
      check_out_date: itemMetadata.check_out_date,
      check_in_time: itemMetadata.check_in_time,
      check_out_time: itemMetadata.check_out_time,
      number_of_guests: totalGuests || itemMetadata.number_of_guests || 1,
      number_of_rooms: totalRooms || itemMetadata.number_of_rooms || 1,
    };
  };

  // Extract stay details from order items
  const stayDetails = extractStayDetailsFromOrderItems();

  // Extract booking data from concierge order for backward compatibility
  const booking = {
    ...conciergeOrder,
    // Map order data to booking fields for existing components
    guest_name: conciergeOrder.order?.metadata?.guest_name,
    guest_email:
      conciergeOrder.order?.metadata?.guest_email ||
      conciergeOrder.order?.email,
    guest_phone: conciergeOrder.order?.metadata?.guest_phone,

    // Hotel stay information - prioritize order items metadata, fallback to order metadata
    hotel_id: stayDetails.hotel_id || conciergeOrder.order?.metadata?.hotel_id,
    hotel_name:
      stayDetails.hotel_name || conciergeOrder.order?.metadata?.hotel_name,
    room_config_name:
      stayDetails.room_config_name ||
      conciergeOrder.order?.metadata?.room_config_name ||
      conciergeOrder.order?.metadata?.room_name,
    room_type:
      stayDetails.room_type || conciergeOrder.order?.metadata?.room_type,
    check_in_date:
      stayDetails.check_in_date ||
      conciergeOrder.order?.metadata?.check_in_date,
    check_out_date:
      stayDetails.check_out_date ||
      conciergeOrder.order?.metadata?.check_out_date,
    check_in_time:
      stayDetails.check_in_time ||
      conciergeOrder.order?.metadata?.check_in_time,
    check_out_time:
      stayDetails.check_out_time ||
      conciergeOrder.order?.metadata?.check_out_time,
    number_of_guests:
      stayDetails.number_of_guests ||
      conciergeOrder.order?.metadata?.number_of_guests,
    number_of_rooms:
      stayDetails.number_of_rooms ||
      conciergeOrder.order?.metadata?.number_of_rooms,

    // Other booking information
    special_requests: conciergeOrder.order?.metadata?.special_requests,
    total_amount: conciergeOrder.order?.metadata?.total_amount,
    currency_code:
      conciergeOrder.order?.currency_code ||
      conciergeOrder.order?.metadata?.currency_code,
    payment_status:
      conciergeOrder.order?.metadata?.payment_status ||
      conciergeOrder.order?.status,
    travelers: conciergeOrder.order?.metadata?.travelers,
    reservations: conciergeOrder.order?.metadata?.reservations,
    meal_plan: conciergeOrder.order?.metadata?.meal_plan,

    // Keep concierge-specific fields
    assigned_to: conciergeOrder.assigned_to,
    last_contacted_at: conciergeOrder.last_contacted_at,
    concierge_order_items: conciergeOrder.concierge_order_items,
    order: conciergeOrder.order,
  };

  console.log("Booking:", booking);
  console.log("Concierge Order:", conciergeOrder);

  return (
    <div className="gap-4">
      <Toaster />
      <Container className="divide-y p-0 mb-3">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Heading level="h1" className="text-2xl">
              {booking.guest_name || "Guest Booking"}
            </Heading>
            <Badge>
              {conciergeOrder.status?.replace("_", " ") || "Not Started"}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            {hasPermission("concierge_management:update") && (
              <div className="flex items-center gap-2">
                {/* <IconButton size="small">
                  <Edit className="h-4 w-4" />
                </IconButton> */}
                <Button variant="secondary" onClick={handleGoBack} size="small">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Bookings
                </Button>
              </div>
            )}
          </div>
        </div>
      </Container>
      <TwoColumnPage
        widgets={{
          before: [],
          after: [],
          sideBefore: [],
          sideAfter: [],
        }}
        showJSON={false}
        showMetadata={false}
        data={booking}
        hasOutlet={false}
      >
        <TwoColumnPage.Main>
          {/* Enhanced Left Column - Main Content Area */}
          <ConciergeBookingCustomerSection
            booking={booking}
            onCustomerUpdate={fetchBookingDetails}
          />

          {/* 1. Booking Details Section (Priority: High) */}
          <BookingDetailsSection booking={conciergeOrder} />

          {/* Add-ons Table */}
          <ConciergeBookingAddOnsTable
            booking={booking}
            addOns={booking?.concierge_order_items || []}
            onAddOnAdded={() => {
              fetchBookingDetails();
              refetchItems();
            }}
          />

          {/* 3. Quick Actions Section (Priority: Low) - Preserve existing functionality */}
          <ConciergeBookingTabsSection
            booking={booking}
            currentAssignedTo={currentAssignedTo}
          />
        </TwoColumnPage.Main>

        {/* <ConciergeBookingTasksSection booking={booking} /> */}

        <TwoColumnPage.Sidebar>
          {/* Enhanced Right Column - Sidebar */}

          {/* Block 1: Concierge Management Actions (Priority: Medium) */}
          <ConciergeManagementActions
            booking={conciergeOrder}
            onUpdate={fetchBookingDetails}
            onAssignedToChange={setCurrentAssignedTo}
          />

          {/* Block 2: Itinerary Management (Priority: Medium) */}
          <ConciergeBookingItinerarySection
            booking={booking}
            hotelDetails={hotelDetails}
            onItineraryCreated={fetchBookingDetails}
          />
        </TwoColumnPage.Sidebar>
      </TwoColumnPage>
    </div>
  );
};

export default ConciergeBookingDetailPageClient;
