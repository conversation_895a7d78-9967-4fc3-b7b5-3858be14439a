import { format, differenceInDays } from "date-fns";

// Types for API responses
export interface VariantData {
  id: string;
  title: string;
  product_id: string;
  metadata?: Record<string, any>;
}

export interface RoomConfigData {
  id: string;
  title: string;
  metadata?: Record<string, any>;
}

export interface StayDatesInfo {
  dateRange: string;
  nights: number;
  checkInDate?: Date;
  checkOutDate?: Date;
}

export interface HotelInfo {
  name: string;
  occupancy: string;
  mealPlan: string;
}

/**
 * Fetch product variant data by variant ID
 */
export const fetchVariantData = async (variantId: string): Promise<VariantData | null> => {
  try {
    console.log(`Fetching variant data for: ${variantId}`);
    
    // Try multiple endpoints to get variant data
    const endpoints = [
      `/admin/products/variants/${variantId}`,
      `/admin/direct-rooms/variants/${variantId}`,
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint);
        if (response.ok) {
          const data = await response.json();
          const variant = data.variant || data;
          
          if (variant && variant.id) {
            console.log(`✅ Found variant data from ${endpoint}:`, variant.title);
            return variant;
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${endpoint}:`, error);
        continue;
      }
    }

    console.warn(`❌ Could not fetch variant data for: ${variantId}`);
    return null;
  } catch (error) {
    console.error("Error fetching variant data:", error);
    return null;
  }
};

/**
 * Fetch room configuration data by room config ID
 */
export const fetchRoomConfigData = async (roomConfigId: string): Promise<RoomConfigData | null> => {
  try {
    console.log(`Fetching room config data for: ${roomConfigId}`);
    
    // Try multiple endpoints to get room config data
    const endpoints = [
      `/admin/room-configs/${roomConfigId}`,
      `/admin/hotel-management/room-configs/${roomConfigId}`,
      `/admin/direct-room-configs/${roomConfigId}`,
      `/admin/products/${roomConfigId}`,
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint);
        if (response.ok) {
          const data = await response.json();
          const roomConfig = data.product || data.roomConfig || data;
          
          if (roomConfig && roomConfig.id) {
            console.log(`✅ Found room config data from ${endpoint}:`, roomConfig.title);
            return roomConfig;
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${endpoint}:`, error);
        continue;
      }
    }

    console.warn(`❌ Could not fetch room config data for: ${roomConfigId}`);
    return null;
  } catch (error) {
    console.error("Error fetching room config data:", error);
    return null;
  }
};

/**
 * Format stay dates and calculate number of nights
 */
export const formatStayDates = (checkInDate?: string, checkOutDate?: string): StayDatesInfo => {
  if (!checkInDate || !checkOutDate) {
    return { 
      dateRange: "Dates not specified", 
      nights: 0 
    };
  }

  try {
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    
    // Validate dates
    if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
      return { 
        dateRange: "Invalid dates", 
        nights: 0 
      };
    }

    const nights = differenceInDays(checkOut, checkIn);
    
    // Ensure nights is positive
    if (nights <= 0) {
      return { 
        dateRange: "Invalid date range", 
        nights: 0 
      };
    }

    const dateRange = `${format(checkIn, "MMM d, yyyy")} – ${format(checkOut, "MMM d, yyyy")} (${nights} night${nights !== 1 ? 's' : ''})`;
    
    return { 
      dateRange, 
      nights, 
      checkInDate: checkIn, 
      checkOutDate: checkOut 
    };
  } catch (error) {
    console.error("Error formatting dates:", error);
    return { 
      dateRange: "Error formatting dates", 
      nights: 0 
    };
  }
};

/**
 * Format currency amount
 */
export const formatCurrency = (amount: number, currencyCode: string = "USD"): string => {
  try {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode.toUpperCase(),
    }).format(amount / 100); // Convert from cents
  } catch (error) {
    console.error("Error formatting currency:", error);
    return `${currencyCode} ${(amount / 100).toFixed(2)}`;
  }
};

/**
 * Extract hotel information from order item metadata
 */
export const extractHotelInfo = (metadata?: Record<string, any>): HotelInfo => {
  return {
    name: metadata?.hotel_name || "Hotel",
    occupancy: metadata?.occupancy_type || metadata?.guest_type || "Adult",
    mealPlan: metadata?.meal_plan || metadata?.meal_type || "No Meals"
  };
};

/**
 * Get room name from various sources
 */
export const getRoomName = (
  variantData?: VariantData | null, 
  orderItemTitle?: string, 
  metadata?: Record<string, any>
): string => {
  // Priority: variant title > order item title > metadata room name > fallback
  if (variantData?.title) {
    return variantData.title;
  }
  
  if (orderItemTitle && orderItemTitle !== "Room" && orderItemTitle !== "Hotel Room") {
    return orderItemTitle;
  }
  
  if (metadata?.room_name) {
    return metadata.room_name;
  }
  
  if (metadata?.room_type) {
    return metadata.room_type;
  }
  
  return "Room";
};

/**
 * Get room configuration name from various sources
 */
export const getRoomConfigName = (
  roomConfigData?: RoomConfigData | null, 
  metadata?: Record<string, any>
): string => {
  // Priority: room config title > metadata room config name
  if (roomConfigData?.title) {
    return roomConfigData.title;
  }
  
  if (metadata?.room_config_name) {
    return metadata.room_config_name;
  }
  
  return "";
};

/**
 * Calculate total price for stay
 */
export const calculateTotalPrice = (
  unitPrice: number, 
  quantity: number, 
  nights: number
): number => {
  return unitPrice * quantity * Math.max(nights, 1); // Ensure at least 1 night
};

/**
 * Batch fetch data for multiple order items
 */
export const batchFetchStayData = async (orderItems: any[]) => {
  const variantIds = orderItems
    .map(item => item.variant_id)
    .filter(Boolean);
    
  const roomConfigIds = orderItems
    .map(item => item.metadata?.room_config_id)
    .filter(Boolean);

  // Fetch all variant data in parallel
  const variantPromises = variantIds.map(id => fetchVariantData(id));
  const roomConfigPromises = roomConfigIds.map(id => fetchRoomConfigData(id));

  const [variants, roomConfigs] = await Promise.all([
    Promise.all(variantPromises),
    Promise.all(roomConfigPromises)
  ]);

  // Create lookup maps
  const variantMap = new Map();
  variants.forEach((variant, index) => {
    if (variant) {
      variantMap.set(variantIds[index], variant);
    }
  });

  const roomConfigMap = new Map();
  roomConfigs.forEach((roomConfig, index) => {
    if (roomConfig) {
      roomConfigMap.set(roomConfigIds[index], roomConfig);
    }
  });

  return { variantMap, roomConfigMap };
};
