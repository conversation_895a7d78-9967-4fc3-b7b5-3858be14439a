import React from "react";
import { Table, Text, Badge } from "@camped-ai/ui";
import { Package, Wrench } from "lucide-react";
import { SupplierOrderItem } from "../../../../../hooks/vendor-management/use-supplier-orders";

interface OrderItemsTableProps {
  items: SupplierOrderItem[];
}

const OrderItemsTable: React.FC<OrderItemsTableProps> = ({ items }) => {
  // Helper functions
  const formatCurrency = (amount: number, currencyCode: string = "CHF") => {
    return new Intl.NumberFormat("en-CH", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getItemTypeIcon = (itemType: string) => {
    switch (itemType) {
      case "product":
        return <Package className="h-4 w-4" />;
      case "service":
        return <Wrench className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getItemTypeBadgeVariant = (itemType: string) => {
    switch (itemType) {
      case "product":
        return "blue";
      case "service":
        return "green";
      default:
        return "grey";
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "pending":
        return "orange";
      case "confirmed":
        return "green";
      case "in_progress":
        return "blue";
      case "completed":
        return "purple";
      case "cancelled":
        return "grey";
      default:
        return "grey";
    }
  };

  if (!items || items.length === 0) {
    return (
      <div className="flex items-center justify-center py-8 text-center">
        <div>
          <Package className="h-8 w-8 mx-auto text-ui-fg-muted mb-2" />
          <Text className="text-ui-fg-muted">No items in this order</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg border border-ui-border-base">
      <Table>
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>Item</Table.HeaderCell>
            <Table.HeaderCell>Type</Table.HeaderCell>
            <Table.HeaderCell>Quantity</Table.HeaderCell>
            <Table.HeaderCell>Unit Price</Table.HeaderCell>
            <Table.HeaderCell>Total Price</Table.HeaderCell>
            <Table.HeaderCell>Status</Table.HeaderCell>
            <Table.HeaderCell>Service Date</Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {items.map((item) => (
            <Table.Row key={item.id}>
              <Table.Cell>
                <div className="flex items-center gap-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
                    {getItemTypeIcon(item.item_type)}
                  </div>
                  <div>
                    <Text className="font-medium">{item.item_name}</Text>
                    {item.item_description && (
                      <Text className="text-ui-fg-muted text-sm">
                        {item.item_description}
                      </Text>
                    )}
                    {item.product_sku && (
                      <Text className="text-ui-fg-muted text-xs">
                        SKU: {item.product_sku}
                      </Text>
                    )}
                  </div>
                </div>
              </Table.Cell>
              <Table.Cell>
                <Badge 
                  variant={getItemTypeBadgeVariant(item.item_type)} 
                  size="small"
                >
                  {item.item_type}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <Text>{item.quantity}</Text>
                {item.service_duration_minutes && (
                  <Text className="text-ui-fg-muted text-sm">
                    {item.service_duration_minutes} min
                  </Text>
                )}
              </Table.Cell>
              <Table.Cell>
                <Text>{formatCurrency(item.unit_price)}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text className="font-medium">
                  {formatCurrency(item.total_price)}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Badge 
                  variant={getStatusBadgeVariant(item.status)} 
                  size="small"
                >
                  {item.status}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                {item.service_date ? (
                  <Text>{formatDate(item.service_date)}</Text>
                ) : (
                  <Text className="text-ui-fg-muted">—</Text>
                )}
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      
      {/* Summary Row */}
      <div className="border-t border-ui-border-base bg-ui-bg-subtle px-6 py-4">
        <div className="flex justify-between items-center">
          <Text className="font-medium">
            Total Items: {items.length}
          </Text>
          <Text className="font-semibold">
            Total: {formatCurrency(
              items.reduce((sum, item) => sum + item.total_price, 0)
            )}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default OrderItemsTable;
