import {
  Buildings,
  PlusMini,
  Adjustments,
} from "@camped-ai/icons";
import {
  Upload as UploadIcon,
  Download,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  FocusModal,
  Toaster,
  toast,
  Select,
} from "@camped-ai/ui";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./[slug]/modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import { HotelFormData } from "../../../components/hotel-form-modern";
import HotelFormModern from "../../../components/hotel-form-modern";
import { HotelData } from "../../../types";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";


import { useRbac } from "../../../hooks/use-rbac";
import HotelCard from "../../../components/hotel-management/HotelCard";
import HotelListItem from "../../../components/hotel-management/HotelListItem";
import { useImagePreloader } from "../../../hooks/useImagePreloader";
import { useImagePerformance } from "../../../hooks/useImagePerformance";
import { useHotelManagementWithDestinations } from "../../../hooks/hotel-management";


import BulkImportModal from "../../../components/hotel/bulk-import-modal";
import ExportModal from "../../../components/hotel/export-modal";
import { HotelFilters } from "../../../components/hotel-management/hotel-filters";

interface Hotel extends Omit<HotelData, "description" | "website" | "email"> {
  destination_name?: string;
  image_url?: string;
  star?: number;
  is_internal?: boolean;
  is_featured?: boolean;
  description?: string; // Override to make optional to match API response
  website?: string; // Override to match API response (undefined instead of null)
  email?: string; // Override to match API response (undefined instead of null)
}

const PageClient = () => {
  const navigate = useNavigate();
  const { hasPermission, hasAnyPermission } = useRbac();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current page, page size, and search term from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "10");
  const searchTerm = searchParams.get("q") || "";

  // Form data for hotel creation
  const [formData, setFormData] = useState<HotelFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    is_pets_allowed: false,
    website: null,
    email: null,
    destination_id: "",
    check_in_time: "14:00",
    check_out_time: "11:00",
    media: [], // Initialize with empty media array
    image_ids: [], // Initialize with empty image IDs array
  });

  // Get filter values from URL params
  const filterDestination = searchParams.get("destination") || undefined;
  const filterFeatured = searchParams.get("featured") === "true" ? true :
                        searchParams.get("featured") === "false" ? false : null;
  const filterStars = searchParams.get("stars") ?
                     searchParams.get("stars")!.split(",").map(Number).filter(n => !isNaN(n)) :
                     [];

  // Use the hotel management hook to fetch data
  const {
    hotels: queryHotels,
    isLoading: queryIsLoading,
    destinationsData,
    totalCount: queryTotalCount,
  } = useHotelManagementWithDestinations({
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
    search: searchTerm || undefined,
    destination_id: filterDestination,
    is_featured: filterFeatured === null ? undefined : filterFeatured,
    star_rating: filterStars,
  });

  // Use query data
  const hotels = queryHotels || [];
  const isLoading = queryIsLoading;
  const totalCount = queryTotalCount || 0;
  const destinations = destinationsData?.destinations || [];

  // TODO: Define table filters following Medusa pattern (for future DataTable integration)
  // const tableFilters = useMemo(() => [
  //   {
  //     key: "destination_id",
  //     label: "Destination",
  //     type: "select",
  //     options: [
  //       { label: "All Destinations", value: "" },
  //       ...destinations.map((dest) => ({
  //         label: dest.name,
  //         value: dest.id,
  //       })),
  //     ],
  //   },
  //   {
  //     key: "is_featured",
  //     label: "Featured Status",
  //     type: "select",
  //     options: [
  //       { label: "All", value: "" },
  //       { label: "Featured", value: "true" },
  //       { label: "Not Featured", value: "false" },
  //     ],
  //   },
  //   {
  //     key: "rating",
  //     label: "Star Rating",
  //     type: "select",
  //     options: [
  //       { label: "All Ratings", value: "" },
  //       { label: "1 Star", value: "1" },
  //       { label: "2 Stars", value: "2" },
  //       { label: "3 Stars", value: "3" },
  //       { label: "4 Stars", value: "4" },
  //       { label: "5 Stars", value: "5" },
  //     ],
  //   },
  // ], [destinations]);

  // TODO: Define sortable columns (for future DataTable integration)
  // const orderBy = useMemo(() => [
  //   { key: "name" as keyof Hotel, label: "Hotel Name" },
  //   { key: "destination_name" as keyof Hotel, label: "Destination" },
  //   { key: "rating" as keyof Hotel, label: "Rating" },
  //   { key: "created_at" as keyof Hotel, label: "Created At" },
  // ], []);







  // Helper functions to update URL parameters
  const updateParams = useCallback((updates: Record<string, any>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        newParams.delete(key);
      } else if (Array.isArray(value)) {
        if (value.length === 0) {
          newParams.delete(key);
        } else {
          newParams.set(key, value.join(","));
        }
      } else {
        newParams.set(key, String(value));
      }
    });
    setSearchParams(newParams, { replace: true });
  }, [searchParams, setSearchParams]);

  const updateParamsImmediate = useCallback((updates: Record<string, any>) => {
    updateParams(updates);
  }, [updateParams]);

  // Simple search input state - initialize from URL
  const [searchInput, setSearchInput] = useState(searchTerm);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchTerm);

  // UI states - read directly from URL for simplicity
  const showFilters = useMemo(() => {
    const filtersParam = searchParams.get("filters");
    return filtersParam === "true";
  }, [searchParams]);

  const viewMode = useMemo(() => {
    const viewParam = searchParams.get("view");
    return (viewParam as "grid" | "list") || "grid";
  }, [searchParams]);

  // Image optimization hooks
  const { logPerformanceStats } = useImagePerformance();

  // Log performance stats periodically (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const interval = setInterval(() => {
        logPerformanceStats();
      }, 30000); // Log every 30 seconds

      return () => clearInterval(interval);
    }
  }, [logPerformanceStats]);





  // Simple debounce for search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchInput);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchInput]);

  // Sync debounced search query with URL parameters
  useEffect(() => {
    updateParams({ q: debouncedSearchQuery || undefined });
  }, [debouncedSearchQuery, updateParams]);

  // Sync search input with URL changes (for browser back/forward)
  useEffect(() => {
    if (searchTerm !== searchInput) {
      setSearchInput(searchTerm);
      setDebouncedSearchQuery(searchTerm);
    }
  }, [searchTerm]);



  // Extract image URLs for preloading
  const actualPriorityImages = hotels
    .slice(0, viewMode === "grid" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  const actualNonPriorityImages = hotels
    .slice(viewMode === "grid" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  // Preload priority images immediately, others with delay
  useImagePreloader({ images: actualPriorityImages, priority: true });
  useImagePreloader({ images: actualNonPriorityImages, priority: false });

  // Modal/Drawer states with URL synchronization
  const open = useMemo(() => searchParams.get("add") === "true", [searchParams]);
  const bulkImportOpen = useMemo(() => searchParams.get("import") === "true", [searchParams]);
  const exportModalOpen = useMemo(() => searchParams.get("export") === "true", [searchParams]);

  // Listen for custom events
  useEffect(() => {
    const handleCloseModal = (event: any) => {
      updateBulkImportOpen(false);
      // Only refresh if explicitly requested
      if (event.detail?.refresh) {
        setTimeout(() => {
          console.log("Refreshing hotels after import");
          // Will be handled by the URL parameter change effect
        }, 500);
      }
    };

    const handleRefreshData = () => {
      // Refresh data without closing the modal
      setTimeout(() => {
        console.log("Refreshing hotels after successful import");
        // Will be handled by the URL parameter change effect
      }, 500);
    };

    window.addEventListener("closeHotelModal", handleCloseModal);
    window.addEventListener("refreshHotelData", handleRefreshData);

    return () => {
      window.removeEventListener("closeHotelModal", handleCloseModal);
      window.removeEventListener("refreshHotelData", handleRefreshData);
    };
  }, [destinations]);

  // Ref to access the form's submit function
  const formSubmitRef = useRef<(() => Promise<void>) | null>(null);

  // Optimized update functions using the custom hook
  const updateShowFilters = useCallback(
    (value: boolean) => {
      updateParamsImmediate({ filters: value }); // Immediate URL update for UI responsiveness
    },
    [updateParamsImmediate]
  );

  const updateFilterDestination = useCallback(
    (value: string | undefined) => {
      updateParams({ destination: value || "" });
    },
    [updateParams]
  );

  const updateFilterStars = useCallback(
    (value: number[]) => {
      updateParams({ stars: value });
    },
    [updateParams]
  );

  const updateFilterFeatured = useCallback(
    (value: boolean | null) => {
      updateParams({ featured: value });
    },
    [updateParams]
  );

  const updateViewMode = useCallback(
    (value: "grid" | "list") => {
      updateParamsImmediate({ view: value }); // Immediate URL update for UI responsiveness
    },
    [updateParamsImmediate]
  );

  const updateOpen = useCallback(
    (value: boolean) => {
      updateParams({ add: value });
    },
    [updateParams]
  );

  const updateBulkImportOpen = useCallback(
    (value: boolean) => {
      updateParams({ import: value });
    },
    [updateParams]
  );

  const updateExportModalOpen = useCallback(
    (value: boolean) => {
      updateParams({ export: value });
    },
    [updateParams]
  );

  // Legacy fetch functions removed - now handled by TanStack Query hooks
  // fetchDestinations() -> useDestinationsForHotelManagement()
  // fetchRoomTypes() -> removed (not needed for this page)

  // fetchDestinationDetails removed - destinations are now pre-loaded via TanStack Query

  // fetchHotels function removed - now handled by useHotelManagementWithDestinations hook

  // All hotel fetching logic removed - now handled by TanStack Query hooks

  // Create a stable reference for filter values to detect actual changes
  const filterValues = useMemo(() => ({
    destination: filterDestination,
    featured: filterFeatured,
    stars: filterStars,
    search: debouncedSearchQuery,
  }), [filterDestination, filterFeatured, filterStars, debouncedSearchQuery]);

  // Track previous filter values to detect changes
  const previousFilters = useRef(filterValues);

  // Reset to page 1 only when filters actually change (not when they're just active)
  useEffect(() => {
    const prev = previousFilters.current;

    // Check if any filter actually changed - using deep comparison for arrays
    const filtersChanged =
      prev.destination !== filterValues.destination ||
      prev.featured !== filterValues.featured ||
      JSON.stringify(prev.stars) !== JSON.stringify(filterValues.stars) ||
      prev.search !== filterValues.search;

    // Debug logging (development only)
    if (process.env.NODE_ENV === "development") {
      console.log("🔍 Filter change check:", {
        filtersChanged,
        currentPage,
        previousFilters: prev,
        currentFilters: filterValues,
        willResetPage: filtersChanged && currentPage !== 1
      });
    }

    // Only reset to page 1 if filters changed AND we're not already on page 1
    if (filtersChanged && currentPage !== 1) {
      console.log("📄 Resetting to page 1 due to filter change");
      updateParamsImmediate({ page: "1" });
    }

    // Update previous filters for next comparison
    previousFilters.current = filterValues;
  }, [filterValues, currentPage, updateParamsImmediate]);

  // Pagination handlers - using URL parameters
  const handlePageChange = useCallback(
    (page: number) => {
      // Debug logging (development only)
      if (process.env.NODE_ENV === "development") {
        console.log("🔄 Page change requested:", {
          fromPage: currentPage,
          toPage: page,
          pageSize,
        });
      }

      updateParamsImmediate({
        page: page.toString(),
        limit: pageSize.toString(),
      });
      // TanStack Query will automatically refetch when URL params change
    },
    [updateParamsImmediate, pageSize, currentPage]
  );

  const totalPages = Math.ceil(totalCount / pageSize);

  const handleCreate = async (data: HotelFormData) => {
    try {
      // Separate files to upload from existing URLs
      const mediaArray = data.media ?? [];
      const filesToUpload = mediaArray.filter(
        (media) => media.file instanceof File
      );
      // Prepare data for submission without media
      const dataWithoutMedia = { ...data };
      delete dataWithoutMedia.media;
      delete dataWithoutMedia.image_ids;

      const response = await fetch("/admin/hotel-management/hotels", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataWithoutMedia),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to create hotel: ${response.status} ${response.statusText}`
        );
      }

      const responseData = await response.json();

      // Upload new files if any
      if (filesToUpload.length > 0) {
        const uploadPromises = filesToUpload.map(async (mediaFile) => {
          if (!(mediaFile.file instanceof File)) {
            throw new Error("Invalid file");
          }

          const formData = new FormData();
          formData.append("files", mediaFile.file);

          try {
            const hotelId = responseData?.hotel?.id;
            if (!hotelId) {
              throw new Error("No hotel ID available for image upload");
            }

            const response = await fetch(
              `/admin/hotel-management/hotels/${hotelId}/upload`,
              {
                method: "POST",
                body: formData,
                credentials: "include",
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Upload error response:", errorText);
              throw new Error(`File upload failed: ${errorText}`);
            }

            const uploadedFiles = await response.json();
            const uploadedFile = uploadedFiles[0];

            return {
              ...uploadedFile,
              isThumbnail: mediaFile.isThumbnail,
            };
          } catch (error) {
            console.error("File upload error:", error);
            throw error;
          }
        });

        await Promise.all(uploadPromises);
      }

      if (responseData.hotel) {
        // TanStack Query will automatically refetch hotels after mutation

        toast.success("Success", {
          description: "Hotel created successfully",
        });

        updateOpen(false);
        // Reset form data completely, including media array
        setFormData({
          name: "",
          handle: "",
          description: "",
          is_active: true,
          is_featured: false,
          is_pets_allowed: false,
          website: null,
          email: null,
          destination_id: "",
          check_in_time: "14:00",
          check_out_time: "11:00",
          media: [], // Explicitly reset media array
          image_ids: [], // Reset image IDs
          thumbnail_image_id: undefined, // Reset thumbnail
        });

        return true; // Return true on success
      } else {
        throw new Error("No hotel data returned from API");
      }
    } catch (error) {
      console.error("Error creating hotel:", error);
      toast.error("Error", {
        description: "Failed to create hotel",
      });
      return false; // Return false on error
    }
  };

  // All filtering is now handled server-side at the database level
  // No client-side filtering needed - use hotels directly
  const filteredHotels = hotels;



  const handleClearAllFilters = useCallback(() => {
    updateFilterDestination(undefined);
    updateFilterStars([]);
    updateFilterFeatured(null);
    setSearchInput("");
    setDebouncedSearchQuery("");
    updateParams({
      q: undefined,
      destination: undefined,
      stars: undefined,
      featured: undefined,
    });
  }, [updateFilterDestination, updateFilterStars, updateFilterFeatured, updateParams]);

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Hotels</Heading>
            <Text className="text-muted-foreground">
              Manage your hotel properties
            </Text>
          </div>

          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() => updateExportModalOpen(true)}
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
            {hasAnyPermission(["hotel_management:bulk_import", "hotel_management:bulk_operations"]) && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => updateBulkImportOpen(true)}
              >
                <UploadIcon className="w-4 h-4 mr-1" />
                Import
              </Button>
            )}
            {hasPermission("hotel_management:create") && (
              <Button
                variant="primary"
                size="small"
                onClick={() => updateOpen(true)}
              >
                <PlusMini className="w-4 h-4 mr-1" />
                Add Hotel
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters Section */}
        <div className="px-6 py-4 space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Input
                placeholder="Search hotels..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="h-9"
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => updateShowFilters(!showFilters)}
                className="whitespace-nowrap"
              >
                <Adjustments className="w-4 h-4 mr-2" />
                {showFilters ? "Hide Filters" : "Show Filters"}
              </Button>

              <div className="flex gap-2">
                <Button
                  variant={viewMode === "grid" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => updateViewMode("grid")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-5 h-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </Button>

                <Button
                  variant={viewMode === "list" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => updateViewMode("list")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-5 h-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </div>

          {/* Active filters summary - only shown when filters are applied */}
          {(filterDestination ||
            filterStars.length > 0 ||
            filterFeatured !== null ||
            debouncedSearchQuery) &&
            !showFilters && (
              <div className="flex flex-wrap items-center gap-1.5 bg-blue-50 dark:bg-blue-950/30 border border-blue-100 dark:border-blue-800 rounded-md p-2 text-xs">
              <span className="font-medium text-blue-700 dark:text-blue-300">
                Active filters:
              </span>

              {debouncedSearchQuery && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>Search: "{debouncedSearchQuery}"</span>
                  <button
                    onClick={() => {
                      setSearchInput("");
                      setDebouncedSearchQuery("");
                      updateParams({ search: "" });
                    }}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterDestination && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>
                    Destination:{" "}
                    {destinations.find((d) => d.id === filterDestination)
                      ?.name || "Unknown"}
                  </span>
                  <button
                    onClick={() => updateFilterDestination(undefined)}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterStars.length > 0 && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>Stars: {filterStars.sort().join(", ")}★</span>
                  <button
                    onClick={() => updateFilterStars([])}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterFeatured !== null && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>
                    Status: {filterFeatured ? "Featured" : "Not Featured"}
                  </span>
                  <button
                    onClick={() => updateFilterFeatured(null)}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              <button
                onClick={() => updateShowFilters(true)}
                className="ml-auto bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-md transition-colors flex items-center gap-1 text-xs"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Edit Filters</span>
              </button>
            </div>
          )}

        {showFilters && (
          <HotelFilters
            filterDestination={filterDestination}
            filterStars={filterStars}
            filterFeatured={filterFeatured}
            destinations={destinations}
            totalCount={totalCount}
            onDestinationChange={updateFilterDestination}
            onStarsChange={updateFilterStars}
            onFeaturedChange={updateFilterFeatured}
            onClearAll={handleClearAllFilters}
          />
        )}
        </div>

        {/* Content Section */}
        <div className="px-6 py-4">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="h-64 animate-pulse bg-muted rounded-lg"
              ></div>
            ))}
          </div>
        ) : filteredHotels.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredHotels.map((hotel, index) => (
                <HotelCard
                  key={hotel.id}
                  hotel={{
                    ...hotel,
                    status: hotel.is_active ? "active" : "inactive",
                  }}
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                  priority={index < 6} // Prioritize first 6 images (2 rows)
                />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHotels.map((hotel, index) => (
                <HotelListItem
                  key={hotel.id}
                  hotel={{
                    ...hotel,
                    status: hotel.is_active ? "active" : "inactive",
                  }}
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                  priority={index < 3} // Prioritize first 3 images in list view
                />
              ))}
            </div>
          )
        ) : (
          <div className="flex flex-col items-center text-center py-12 bg-muted rounded-lg">
            <div className="flex items-center">
              <Buildings className="w-8 h-8 text-muted-foreground mb-0.5" />
              <Text className="text-muted-foreground mb-4">
                {debouncedSearchQuery ||
                filterDestination ||
                filterStars.length > 0 ||
                filterFeatured !== null
                  ? "No hotels match your search criteria"
                  : "No hotels found"}
              </Text>
            </div>
            {hasPermission("hotel_management:create") && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => updateOpen(true)}
              >
                <PlusMini className="w-4 h-4 mr-2" />
                Add your first hotel
              </Button>
            )}
          </div>
        )}
        </div>

        {/* Pagination */}
        {totalCount > 0 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="flex items-center gap-2">
              <Text className="text-sm text-muted-foreground">
                Total Hotels: {totalCount}
              </Text>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <div className="flex items-center gap-1">
                {(() => {
                  const maxVisiblePages = 5;
                  const startPage = Math.max(
                    1,
                    currentPage - Math.floor(maxVisiblePages / 2)
                  );
                  const endPage = Math.min(
                    totalPages,
                    startPage + maxVisiblePages - 1
                  );
                  const adjustedStartPage = Math.max(
                    1,
                    endPage - maxVisiblePages + 1
                  );

                  const pages = [];
                  for (let i = adjustedStartPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        variant={i === currentPage ? "primary" : "secondary"}
                        size="small"
                        onClick={() => handlePageChange(i)}
                        className="w-8 h-8 p-0"
                      >
                        {i}
                      </Button>
                    );
                  }
                  return pages;
                })()}
              </div>

              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Text className="text-sm text-muted-foreground">Show per Page:</Text>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  const newLimit = parseInt(value);
                  updateParamsImmediate({
                    limit: newLimit.toString(),
                    page: "1",
                  });
                }}
              >
                <Select.Trigger className="w-[60px] h-8">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="5">5</Select.Item>
                  <Select.Item value="10">10</Select.Item>
                  <Select.Item value="20">20</Select.Item>
                  <Select.Item value="25">25</Select.Item>
                  <Select.Item value="50">50</Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>
        )}
      </Container>

      {/* Overlay and Drawer for Add Hotel */}
      {open && (
        <div
          className="fixed inset-0 bg-black/30 dark:bg-black/50"
          onClick={() => updateOpen(false)}
        />
      )}
      <FocusModal open={open} onOpenChange={updateOpen}>
        <FocusModal.Content className="shadow-lg flex flex-col">
          <HotelFormModern
            formData={formData}
            onSubmit={handleCreate}
            closeModal={() => updateOpen(false)}
            onSubmitRef={formSubmitRef}
          />
        </FocusModal.Content>
      </FocusModal>

      <BulkImportModal
        open={bulkImportOpen}
        onClose={() => {
          updateBulkImportOpen(false);
        }}
      />

      <ExportModal
        open={exportModalOpen}
        onClose={() => updateExportModalOpen(false)}
      />
    </>
  );
};



// Default export for the component
export default PageClient;
