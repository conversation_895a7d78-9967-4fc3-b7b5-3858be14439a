import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Tabs,
} from "@camped-ai/ui";
import { ChevronLeft } from "@camped-ai/icons";
import { Calendar, Settings } from "lucide-react";
import RoomInventoryManager from "../../../../../components/hotel/room-inventory-manager";
import { format, addDays, startOfDay, endOfDay, parseISO } from "date-fns";
import Spinner from "../../../../../components/shared/spinner";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../hooks/use-rbac";

const RoomAvailabilityPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission, hasAnyPermission } = useRbac();
  const [room, setRoom] = useState(null);
  const [roomConfig, setRoomConfig] = useState(null);
  const [hotel, setHotel] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState(startOfDay(new Date()));
  const [endDate, setEndDate] = useState(endOfDay(addDays(new Date(), 14)));
  const [availability, setAvailability] = useState([]);
  const [activeTab, setActiveTab] = useState("view");

  // Fetch room data
  useEffect(() => {
    const fetchRoomData = async () => {
      try {
        setIsLoading(true);

        // Fetch room details
        const roomResponse = await fetch(`/admin/direct-rooms/${id}`);
        if (!roomResponse.ok) {
          throw new Error("Failed to fetch room details");
        }

        const roomData = await roomResponse.json();
        setRoom(roomData.room);

        // Fetch room configuration details
        if (roomData.room?.product_id) {
          const configResponse = await fetch(
            `/admin/direct-room-configs/${roomData.room.product_id}`
          );
          if (configResponse.ok) {
            const configData = await configResponse.json();
            setRoomConfig(configData.room_config);

            // Fetch hotel details
            if (configData.room_config?.hotel_id) {
              const hotelResponse = await fetch(
                `/admin/hotel-management/hotels/${configData.room_config.hotel_id}`
              );
              if (hotelResponse.ok) {
                const hotelData = await hotelResponse.json();
                setHotel(hotelData.hotel);
              }
            }
          }
        }

        // Fetch availability data
        await fetchAvailabilityData();
      } catch (error) {
        console.error("Error fetching room data:", error);
        toast.error("Error", {
          description: error.message || "Failed to fetch room data",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchRoomData();
    }
  }, [id]);

  // Fetch availability data
  const fetchAvailabilityData = async () => {
    try {
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      // Try to get inventory data first
      const inventoryResponse = await fetch(
        `/admin/hotel-management/rooms/${id}/inventory?start_date=${formattedStartDate}&end_date=${formattedEndDate}`
      );

      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();

        if (inventoryData.inventory_item) {
          // Process inventory data
          const availabilityArray = [];
          const dateRange = [];

          // Generate all dates in the range
          let currentDate = new Date(startDate);
          while (currentDate <= endDate) {
            dateRange.push(format(currentDate, "yyyy-MM-dd"));
            currentDate.setDate(currentDate.getDate() + 1);
          }

          // Create a map of dates to availability
          const availabilityMap = new Map();

          if (
            inventoryData.inventory_item.availability &&
            Array.isArray(inventoryData.inventory_item.availability)
          ) {
            for (const item of inventoryData.inventory_item.availability) {
              const date = new Date(item.date);
              availabilityMap.set(format(date, "yyyy-MM-dd"), item);
            }
          }

          // Create availability array
          for (const dateStr of dateRange) {
            const dateAvailability = availabilityMap.get(dateStr);

            availabilityArray.push({
              date: dateStr,
              status: dateAvailability
                ? dateAvailability.quantity > 0
                  ? "available"
                  : "booked"
                : "available",
              quantity: dateAvailability ? dateAvailability.quantity : 1,
              dynamic_price: dateAvailability
                ? dateAvailability.dynamic_price
                : null,
            });
          }

          setAvailability(availabilityArray);
          return;
        }
      }

      // Fall back to room availability records
      const availabilityResponse = await fetch(
        `/admin/room-availability?room_id=${id}&start_date=${formattedStartDate}&end_date=${formattedEndDate}`
      );

      if (availabilityResponse.ok) {
        const availabilityData = await availabilityResponse.json();
        setAvailability(availabilityData.availability || []);
      }
    } catch (error) {
      console.error("Error fetching availability data:", error);
      toast.error("Error", {
        description: "Failed to fetch availability data",
      });
    }
  };

  // Handle date range change
  const handleDateRangeChange = async () => {
    await fetchAvailabilityData();
  };

  // Handle inventory updated
  const handleInventoryUpdated = () => {
    fetchAvailabilityData();
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4">Loading room data...</div>
        </div>
      </>
    );
  }

  if (!room) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="p-8 text-center">
          <Heading level="h2" className="text-xl mb-4">
            Room Not Found
          </Heading>
          <Text className="text-ui-fg-subtle mb-6">
            The room you are looking for could not be found.
          </Text>
          <Button
            variant="secondary"
            onClick={() => navigate("/hotel-management")}
          >
            Back to Hotel Management
          </Button>
          <Toaster />
        </div>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <div className="flex flex-col min-h-full">
        <div className="w-full flex flex-col gap-y-4">
          {/* Header */}
          <div className="border-b border-ui-border-base py-4 px-8">
            <div className="flex items-center gap-x-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => {
                  if (roomConfig && hotel) {
                    navigate(
                      `/hotel-management/hotels/${hotel.id}/rooms/${roomConfig.id}`
                    );
                  } else {
                    navigate("/hotel-management");
                  }
                }}
              >
                <ChevronLeft className="text-ui-fg-subtle" />
                Back to Room
              </Button>
              <h1 className="text-xl font-semibold">Room Availability</h1>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 px-8 pb-8">
            {/* Room Info */}
            <Container className="p-6 mb-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <Heading level="h2" className="text-xl font-medium">
                    {room.title || `Room ${room.id.slice(-4)}`}
                  </Heading>
                  <Text className="text-ui-fg-subtle">
                    {roomConfig?.title || "Room Configuration"} •{" "}
                    {hotel?.name || "Hotel"}
                  </Text>
                </div>
                <div className="flex gap-2">
                  {hasAnyPermission(["rooms:availability", "rooms:view"]) && (
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        if (hotel) {
                          navigate(
                            `/hotel-management/hotels/${hotel.id}/availability`
                          );
                        }
                      }}
                    >
                      View Hotel Availability
                    </Button>
                  )}
                </div>
              </div>
            </Container>

            {/* Availability Management */}
            <Container className="p-0 overflow-hidden">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <Tabs.List className="px-6 pt-4">
                  <Tabs.Trigger value="view">
                    <Calendar className="w-4 h-4 mr-2" />
                    View Availability
                  </Tabs.Trigger>
                  <Tabs.Trigger value="configure">
                    <Settings className="w-4 h-4 mr-2" />
                    Configure Availability
                  </Tabs.Trigger>
                </Tabs.List>

                <Tabs.Content value="view" className="p-6">
                  <div className="mb-6">
                    <div className="flex flex-col md:flex-row gap-4 mb-4 items-end">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-grow">
                        <div>
                          <Text className="text-ui-fg-subtle mb-1">
                            Start Date
                          </Text>
                          <input
                            type="date"
                            value={format(startDate, "yyyy-MM-dd")}
                            onChange={(e) =>
                              setStartDate(startOfDay(new Date(e.target.value)))
                            }
                            className="w-full rounded-md border border-ui-border-base px-4 py-2"
                          />
                        </div>
                        <div>
                          <Text className="text-ui-fg-subtle mb-1">
                            End Date
                          </Text>
                          <input
                            type="date"
                            value={format(endDate, "yyyy-MM-dd")}
                            onChange={(e) =>
                              setEndDate(endOfDay(new Date(e.target.value)))
                            }
                            className="w-full rounded-md border border-ui-border-base px-4 py-2"
                          />
                        </div>
                      </div>
                      <Button
                        variant="primary"
                        size="small"
                        onClick={handleDateRangeChange}
                      >
                        Apply
                      </Button>
                    </div>

                    <div className="bg-ui-bg-subtle p-4 rounded-lg">
                      <Heading level="h3" className="text-lg mb-4">
                        Room Availability Calendar
                      </Heading>

                      {availability.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
                          {availability.map((item, index) => (
                            <div
                              key={index}
                              className={`p-3 rounded-md ${
                                item.status === "available"
                                  ? "bg-green-100"
                                  : item.status === "booked"
                                  ? "bg-blue-100"
                                  : item.status === "maintenance"
                                  ? "bg-orange-100"
                                  : item.status === "on_demand"
                                  ? "bg-yellow-100"
                                  : "bg-red-100"
                              }`}
                            >
                              <Text className="font-medium">
                                {format(parseISO(item.date), "MMM dd")}
                              </Text>
                              <Text className="text-sm capitalize">
                                {item.status}
                              </Text>
                              {item.dynamic_price && (
                                <Text className="text-sm font-medium mt-1">
                                  ${parseFloat(item.dynamic_price).toFixed(2)}
                                </Text>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <Text className="text-ui-fg-subtle">
                            No availability data found for the selected date
                            range.
                          </Text>
                        </div>
                      )}
                    </div>
                  </div>
                </Tabs.Content>

                <Tabs.Content value="configure" className="p-6">
                  <div className="mb-6">
                    <Heading level="h3" className="text-lg mb-4">
                      Configure Room Availability
                    </Heading>
                    <Text className="text-ui-fg-subtle mb-4">
                      Update availability and pricing for this room
                    </Text>

                    <RoomInventoryManager
                      hotelId={hotel?.id || ""}
                      roomId={room.id}
                      roomConfigId={roomConfig?.id || ""}
                      onInventoryUpdated={handleInventoryUpdated}
                    />
                  </div>
                </Tabs.Content>
              </Tabs>
            </Container>
          </div>
        </div>
        <Toaster />
      </div>
    </>
  );
};

export default RoomAvailabilityPage;
