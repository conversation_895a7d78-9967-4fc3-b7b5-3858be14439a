import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../modules/concierge-management";
import { ConciergeOrderStatus } from "../../../../../modules/concierge-management/types";
import { UpdateConciergeOrderStatusWorkflow } from "../../../../../workflows/concierge-management";

// Validation schemas
export const PatchAdminUpdateConciergeOrder = z.object({
  hotel_id: z.string().optional(),
  check_in_date: z.string().datetime().optional(),
  check_out_date: z.string().datetime().optional(),
  assigned_to: z.string().optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(ConciergeOrderStatus).optional(),
  last_contacted_at: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
  priority_level: z.enum(["low", "medium", "high", "urgent"]).optional(),
  // Guest information fields
  guest_name: z.string().min(1).optional(),
  guest_email: z.string().email().optional(),
  guest_phone: z.string().optional(),
});

export type PatchAdminUpdateConciergeOrderType = z.infer<typeof PatchAdminUpdateConciergeOrder>;

// Helper function to update guest information in order metadata
async function updateOrderGuestInfo(
  scope: any,
  orderId: string,
  guestInfo: {
    guest_name?: string;
    guest_email?: string;
    guest_phone?: string;
  }
) {
  const orderService = scope.resolve(Modules.ORDER);

  // Get current order to preserve existing metadata
  const currentOrder = await orderService.retrieveOrder(orderId);

  // Prepare updated metadata, only updating provided fields
  const updatedMetadata = {
    ...currentOrder.metadata,
  };

  if (guestInfo.guest_name !== undefined) {
    updatedMetadata.guest_name = guestInfo.guest_name;
  }
  if (guestInfo.guest_email !== undefined) {
    updatedMetadata.guest_email = guestInfo.guest_email;
  }
  if (guestInfo.guest_phone !== undefined) {
    updatedMetadata.guest_phone = guestInfo.guest_phone;
  }

  // Update the order with new metadata
  await orderService.updateOrders(orderId, {
    metadata: updatedMetadata,
  });
}

// GET endpoint to retrieve a specific concierge order with enhanced data
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    const query = req.scope.resolve("query");

    if (!id) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    const conciergeOrder = await conciergeService.retrieveConciergeOrder(id, {
      relations: ["concierge_order_items"],
    });

    // Fetch enhanced order data if order_id exists
    if (conciergeOrder.order_id) {
      try {
        // Get order data with customer information and line items
        const { data: orders } = await query.graph({
          entity: "order",
          fields: [
            "id",
            "total",
            "currency_code",
            "status",
            "created_at",
            "metadata",
            "customer.id",
            "customer.first_name",
            "customer.last_name",
            "customer.email",
            "customer.phone",
            "customer.metadata",
          ],
          filters: { id: conciergeOrder.order_id },
        });

        if (orders && orders.length > 0) {
          const order = orders[0];
          conciergeOrder.order = order;

          // Get order line items
          try {
            const { data: orderWithLineItems } = await query.graph({
              entity: "order",
              fields: [
                "id",
                "items.id",
                "items.unit_price",
                "items.quantity",
                "items.title",
                "items.variant_id",
                "items.product_id",
                "items.metadata"
              ],
              filters: { id: conciergeOrder.order_id },
            });

            conciergeOrder.order_line_items = orderWithLineItems[0]?.items || [];
            console.log(`[API] Fetched ${conciergeOrder.order_line_items.length} line items for order ${conciergeOrder.order_id}`);
          } catch (lineItemError) {
            console.warn(`[API] Could not fetch line items for order ${conciergeOrder.order_id}:`, lineItemError.message);
            conciergeOrder.order_line_items = [];
          }

          // Get payment collections
          try {
            const { data: orderWithPayments } = await query.graph({
              entity: "order",
              fields: [
                "id",
                "payment_collections.id",
                "payment_collections.amount",
                "payment_collections.captured_amount",
                "payment_collections.authorized_amount",
                "payment_collections.status",
                "payment_collections.currency_code",
                "payment_collections.created_at",
                "payment_collections.updated_at",
                "payment_collections.completed_at",
                "payment_collections.captured_at",
                "payment_collections.metadata"
              ],
              filters: { id: conciergeOrder.order_id },
            });

            conciergeOrder.payment_collections = orderWithPayments[0]?.payment_collections || [];
            console.log(`[API] Fetched ${conciergeOrder.payment_collections.length} payment collections for order ${conciergeOrder.order_id}`);
          } catch (paymentError) {
            console.warn(`[API] Could not fetch payment collections for order ${conciergeOrder.order_id}:`, paymentError.message);
            conciergeOrder.payment_collections = [];
          }

          // Enhanced customer data extraction
          conciergeOrder.customer_id = order.customer?.id;
          conciergeOrder.customer_first_name = order.customer?.first_name;
          conciergeOrder.customer_last_name = order.customer?.last_name;
          conciergeOrder.customer_email = order.customer?.email;
          conciergeOrder.customer_phone = order.customer?.phone;
          conciergeOrder.order_total = order.total;
          conciergeOrder.order_status = order.status;
          conciergeOrder.order_currency_code = order.currency_code;
        }
      } catch (orderError) {
        console.warn(`[API] Failed to fetch order ${conciergeOrder.order_id}:`, orderError.message);
        conciergeOrder.order = null;
        conciergeOrder.order_line_items = [];
        conciergeOrder.payment_collections = [];
      }
    }

    // Fetch hotel details if hotel_id exists
    if (conciergeOrder.hotel_id) {
      try {
        // Try to fetch hotel details using the query API
        const { data: hotels } = await query.graph({
          entity: "hotel",
          fields: [
            "id",
            "name",
            "address",
            "city",
            "country"
          ],
          filters: { id: conciergeOrder.hotel_id },
        });

        if (hotels && hotels.length > 0) {
          conciergeOrder.hotel_name = hotels[0].name || conciergeOrder.hotel_id;
        } else {
          conciergeOrder.hotel_name = conciergeOrder.hotel_id;
        }
      } catch (hotelError) {
        console.warn(`[API] Could not fetch hotel details for ${conciergeOrder.hotel_id}:`, hotelError.message);
        conciergeOrder.hotel_name = conciergeOrder.hotel_id;
      }
    }

    return res.json({
      concierge_order: conciergeOrder
    });
  } catch (error) {
    console.error("Error retrieving concierge order:", error);

    if (error.type === "not_found") {
      return res.status(404).json({
        message: "Concierge order not found"
      });
    }

    return res.status(500).json({
      message: "Internal server error",
      error: error.message
    });
  }
};

// PATCH endpoint to update a concierge order
export const PATCH = async (
  req: MedusaRequest<PatchAdminUpdateConciergeOrderType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    if (!id) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    const validatedData = PatchAdminUpdateConciergeOrder.parse(req.body);

    // Extract guest information fields
    const { guest_name, guest_email, guest_phone, ...conciergeFields } = validatedData;
    const hasGuestUpdates = guest_name !== undefined || guest_email !== undefined || guest_phone !== undefined;

    // If status is being updated, use the workflow
    if (conciergeFields.status) {
      const { result } = await UpdateConciergeOrderStatusWorkflow(req.scope).run({
        input: {
          concierge_order_id: id,
          status: conciergeFields.status,
          updated_by: (req as any).user?.id,
          notes: conciergeFields.notes,
        },
      });

      if (!result.success) {
        return res.status(400).json({
          message: "Failed to update concierge order status",
        });
      }

      // Update guest information in order metadata if provided
      if (hasGuestUpdates) {
        await updateOrderGuestInfo(req.scope, result.concierge_order.order_id, {
          guest_name,
          guest_email,
          guest_phone,
        });
      }

      // Update other concierge fields if provided
      const { status, ...otherFields } = conciergeFields;
      if (Object.keys(otherFields).length > 0) {
        const finalOrder = await conciergeService.updateConciergeOrder(id, otherFields);

        // Fetch the updated order data with customer information if guest info was updated
        if (hasGuestUpdates) {
          const orderService = req.scope.resolve(Modules.ORDER);
          try {
            const orderData = await orderService.retrieveOrder(finalOrder.order_id, {
              relations: ["customer", "items"],
            });
            (finalOrder as any).order = orderData;
          } catch (orderError) {
            console.warn(`Failed to fetch updated order data:`, orderError.message);
          }
        }

        return res.json({
          message: "Concierge order updated successfully",
          concierge_order: finalOrder,
        });
      }

      return res.json({
        message: "Concierge order status updated successfully",
        concierge_order: result.concierge_order,
      });
    } else {
      // Regular update without status change

      // Update guest information in order metadata if provided
      if (hasGuestUpdates) {
        // First get the concierge order to get the order_id
        const conciergeOrder = await conciergeService.retrieveConciergeOrder(id);
        await updateOrderGuestInfo(req.scope, conciergeOrder.order_id, {
          guest_name,
          guest_email,
          guest_phone,
        });
      }

      // Update concierge-specific fields if provided
      if (Object.keys(conciergeFields).length > 0) {
        const updatedOrder = await conciergeService.updateConciergeOrder(id, conciergeFields);
        return res.json({
          message: "Concierge order updated successfully",
          concierge_order: updatedOrder,
        });
      }

      // If only guest info was updated, return success with full order data
      if (hasGuestUpdates) {
        const updatedConciergeOrder = await conciergeService.retrieveConciergeOrder(id);

        // Fetch the updated order data with customer information
        const orderService = req.scope.resolve(Modules.ORDER);
        try {
          const orderData = await orderService.retrieveOrder(updatedConciergeOrder.order_id, {
            relations: ["customer", "items"],
          });
          (updatedConciergeOrder as any).order = orderData;
        } catch (orderError) {
          console.warn(`Failed to fetch updated order data:`, orderError.message);
        }

        return res.json({
          message: "Guest information updated successfully",
          concierge_order: updatedConciergeOrder,
        });
      }

      // No updates provided
      return res.status(400).json({
        message: "No valid fields provided for update",
      });
    }
  } catch (error) {
    console.error("Error updating concierge order:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    if (error.type === "not_found") {
      return res.status(404).json({ 
        message: "Concierge order not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

// DELETE endpoint to delete a concierge order
export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    if (!id) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    await conciergeService.deleteConciergeOrder(id);

    return res.json({
      message: "Concierge order deleted successfully",
      id,
    });
  } catch (error) {
    console.error("Error deleting concierge order:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({ 
        message: "Concierge order not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};
