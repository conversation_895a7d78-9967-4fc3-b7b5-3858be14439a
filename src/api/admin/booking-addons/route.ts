import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { BOOKING_ADD_ONS_MODULE } from "../../../modules/booking-add-ons";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../modules/concierge-management";
import { ConciergeOrderItemStatus } from "../../../modules/concierge-management/types";
import { AddOrderItemsWorkflow } from "../../../workflows/order-management";
import { CreateConciergeOrderItemWorkflow } from "../../../workflows/concierge-management";

// Validation schema for creating booking add-ons
const CreateBookingAddOnSchema = z.object({
  // Accept either concierge_order_id (new) or order_id (legacy) - exactly one must be provided
  concierge_order_id: z.string().min(1).optional(),
  order_id: z.string().min(1).optional(),
  add_on_variant_id: z.string().min(1, "Add-on variant ID is required"),
  add_on_name: z.string().min(1, "Add-on name is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1").default(1),
  unit_price: z.number().min(0, "Unit price must be non-negative"),
  currency_code: z.string().default("CHF"),
  customer_field_responses: z.record(z.any()).default({}),
  add_on_metadata: z.record(z.any()).default({}),
  // New fields for category and date range
  category_id: z.string().nullable().optional(),
  start_date: z.string().nullable().optional(),
  end_date: z.string().nullable().optional(),
}).refine(
  (data) => data.concierge_order_id || data.order_id,
  {
    message: "Either concierge_order_id or order_id must be provided",
    path: ["concierge_order_id"],
  }
).refine(
  (data) => !(data.concierge_order_id && data.order_id),
  {
    message: "Cannot provide both concierge_order_id and order_id",
    path: ["concierge_order_id"],
  }
).refine(
  (data) => {
    // Extract dates from either direct fields or metadata
    const startDate = data.start_date || data.add_on_metadata?.start_date;
    const endDate = data.end_date || data.add_on_metadata?.end_date;

    if (startDate && endDate) {
      try {
        return new Date(startDate) <= new Date(endDate);
      } catch {
        return false;
      }
    }
    return true;
  },
  {
    message: "Start date must be before or equal to end date",
    path: ["end_date"],
  }
);

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Fetching booking add-ons using proper service...");

    // Extract query parameters
    const { order_id, limit = 50, offset = 0 } = req.query;

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
    console.log("✅ BookingAddOnService resolved successfully");

    // If order_id is provided, get add-ons for that specific order
    if (order_id) {
      console.log(`🔍 Fetching add-ons for specific order: ${order_id}`);
      const result = await bookingAddOnService.getBookingAddOns(
        order_id as string,
        req.scope
      );

      res.json({
        booking_addons: result.booking_add_ons,
        count: result.count,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
      return;
    }

    // Otherwise, get all booking add-ons with related data
    console.log("🔍 Fetching all booking add-ons with related data...");

    try {
      // Use the new getAllBookingAddOns method that includes related data
      const result = await bookingAddOnService.getAllBookingAddOns({
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
      }, req.scope);

      console.log(`✅ Found ${result.count} booking add-ons with related data`);

      // Transform the data and fetch related information
      const query = req.scope.resolve("query");

      const formattedAddons = await Promise.all(
        result.booking_add_ons.map(async (addon: any) => {
          // Fetch order data
          let orderData = null;
          if (addon.order_id) {
            try {
              console.log(`🔍 Fetching order data for: ${addon.order_id}`);
              const { data: orders } = await query.graph({
                entity: "order",
                filters: { id: addon.order_id },
                fields: [
                  "id",
                  "display_id",
                  "customer_id",
                  "email",
                  "metadata",
                ],
              });
              orderData = orders?.[0] || null;
              console.log(`✅ Order data:`, orderData ? "Found" : "Not found");
            } catch (orderError) {
              console.log("❌ Could not fetch order data:", orderError);
            }
          }

          // Fetch add-on variant data
          let addOnData = null;
          if (addon.add_on_variant_id) {
            try {
              console.log(
                `🔍 Fetching variant data for: ${addon.add_on_variant_id}`
              );
              const { data: variants } = await query.graph({
                entity: "product_variant",
                filters: { id: addon.add_on_variant_id },
                fields: ["id", "title", "metadata"],
              });
              addOnData = variants?.[0] || null;
              console.log(
                `✅ Variant data:`,
                addOnData ? "Found" : "Not found"
              );
            } catch (variantError) {
              console.log("❌ Could not fetch variant data:", variantError);
            }
          }

          // Debug: Log booking add-on sync status
          console.log(`🔍 Booking Add-on Debug:`, {
            id: addon.id,
            add_on_name: addon.add_on_name,
            supplier_order_id: addon.supplier_order_id,
            order_status: addon.order_status,
            created_at: addon.created_at,
            updated_at: addon.updated_at,
          });
          return {
            id: addon.id,
            order_id: addon.order_id,
            add_on_id: addon.add_on_variant_id,
            add_on_name: addon.add_on_name,
            quantity: addon.quantity,
            unit_price: parseFloat(addon.unit_price || 0),
            total_price: parseFloat(addon.total_price || 0),
            customer_field_responses: addon.customer_field_responses || {},
            // Include supplier order tracking fields
            supplier_order_id: addon.supplier_order_id || null,
            order_status: addon.order_status || null,
            created_at: addon.created_at,
            updated_at: addon.updated_at,
            order: orderData,
            add_on: addOnData
              ? {
                  id: addOnData.id,
                  name: addOnData.title,
                  metadata: addOnData.metadata || {},
                }
              : null,
          };
        })
      );

      res.json({
        booking_addons: formattedAddons,
        count: result.count,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    } catch (serviceError) {
      console.log(
        "Service method failed, returning empty result:",
        serviceError
      );

      // Return empty result if service method fails
      res.json({
        booking_addons: [],
        count: 0,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    }
  } catch (error) {
    console.error("Error fetching booking add-ons:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch booking add-ons",
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Creating new booking add-on...");

    // Validate request body
    const validationResult = CreateBookingAddOnSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const data = validationResult.data;
    console.log("✅ Validated booking add-on data:", data);
    console.log("🔍 Variant ID being used:", data.add_on_variant_id);

    // Determine concierge_order_id - either provided directly or lookup from order_id
    let conciergeOrderId: string;

    if (data.concierge_order_id) {
      // Use provided concierge_order_id directly
      conciergeOrderId = data.concierge_order_id;
      console.log("🔍 Using provided concierge_order_id:", conciergeOrderId);
    } else if (data.order_id) {
      // Lookup concierge_order_id from order_id
      console.log("🔍 Looking up concierge_order_id from order_id:", data.order_id);
      const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
      const conciergeOrder = await conciergeManagementService.retrieveConciergeOrderByOrderId(data.order_id);

      if (!conciergeOrder) {
        return res.status(404).json({
          type: "not_found",
          message: `No concierge order found for order ID ${data.order_id}. Please create a concierge order first.`,
        });
      }

      conciergeOrderId = conciergeOrder.id;
      console.log("✅ Found concierge_order_id:", conciergeOrderId);
    } else {
      // This should not happen due to validation, but just in case
      return res.status(400).json({
        type: "validation_error",
        message: "Either concierge_order_id or order_id must be provided",
      });
    }

    // Get the actual order_id for the workflow
    let actualOrderId: string;

    if (data.order_id) {
      actualOrderId = data.order_id;
    } else {
      // Get order_id from concierge order
      const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
      const conciergeOrder = await conciergeManagementService.retrieveConciergeOrder(conciergeOrderId);
      actualOrderId = conciergeOrder.order_id;
    }

    console.log("🔄 Step 1: Creating order line item via AddOrderItemsWorkflow for order:", actualOrderId);

    // Step 1: Use AddOrderItemsWorkflow to create order line items
    const orderService = req.scope.resolve(Modules.ORDER);
    const order = await orderService.retrieveOrder(actualOrderId);

    if (!order) {
      return res.status(404).json({
        type: "not_found",
        message: `Order with ID ${actualOrderId} not found`,
      });
    }

    // Determine workflow mode based on order status
    const isDraftOrder = order.status === "pending" || order.is_draft_order;
    const workflowMode = isDraftOrder ? "draft" : "confirmed";

    // Prepare order item for workflow
    const orderItem = {
      variant_id: data.add_on_variant_id,
      quantity: data.quantity,
      title: data.add_on_name,
      unit_price: Math.round(data.unit_price), // Ensure it's in cents
      metadata: {
        item_type: "add_on",
        add_on_variant_id: data.add_on_variant_id,
        add_on_name: data.add_on_name,
        currency_code: data.currency_code,
        customer_field_responses: data.customer_field_responses,
        add_on_metadata: data.add_on_metadata,
        supplier_order_id: null,
        order_status: "pending",
        booking_addon_source: true,
        product_id: 'product_add_ons_main',
      },
    };

    // Execute AddOrderItemsWorkflow
    const { result: orderResult } = await AddOrderItemsWorkflow(req.scope).run({
      input: {
        order_id: actualOrderId,
        items: [orderItem],
        mode: workflowMode,
      },
    });

    if (!orderResult || !orderResult.addedItems || orderResult.addedItems.length === 0) {
      throw new Error("Failed to create order line item - no items were added");
    }

    const createdLineItem = orderResult.addedItems[0];
    console.log("✅ Step 1 completed: Order line item created:", createdLineItem.id);

    console.log("🔄 Step 1.5: Retrieving corresponding order_item.id...");

    // Query for the corresponding order_item through the order relationship
    const query = req.scope.resolve("query");
    let orderItemId: string | null = null;

    try {
      // Query the order with its items to find the corresponding order_item
      const { data: orders } = await query.graph({
        entity: "order",
        filters: { id: actualOrderId },
        fields: [
          "id",
          "items.id",
          "items.variant_id",
          "items.quantity",
          "items.unit_price",
          "items.created_at"
        ],
      });

      if (orders && orders.length > 0 && orders[0].items) {
        // Find the most recently created item with matching variant_id
        const matchingItems = orders[0].items
          .filter((item: any) => item.variant_id === data.add_on_variant_id)
          .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        if (matchingItems.length > 0) {
          orderItemId = matchingItems[0].id;
          console.log("✅ Found corresponding order_item.id:", orderItemId);
        } else {
          console.warn("⚠️ Could not find matching order_item by variant_id - will proceed with line_item_id only");
        }
      } else {
        console.warn("⚠️ Could not retrieve order items - will proceed with line_item_id only");
      }
    } catch (error) {
      console.warn("⚠️ Error querying order items:", error.message, "- will proceed with line_item_id only");
    }

    console.log("🔄 Step 2: Creating concierge order item via CreateConciergeOrderItemWorkflow");

    // Step 2: Create concierge order item using the line item data
    const categoryId = data.category_id || data.add_on_metadata?.category_id || null;
    const startDate = data.start_date || data.add_on_metadata?.start_date || null;
    const endDate = data.end_date || data.add_on_metadata?.end_date || null;

    const { result: conciergeResult } = await CreateConciergeOrderItemWorkflow(req.scope).run({
      input: {
        concierge_order_id: conciergeOrderId,
        line_item_id: createdLineItem.id, // Link to the created order line item
        item_id: orderItemId, // Link to the created order item (if found)
        variant_id: data.add_on_variant_id,
        quantity: data.quantity,
        unit_price: data.unit_price,
        title: data.add_on_name,
        status: ConciergeOrderItemStatus.UNDER_REVIEW,
        category_id: categoryId,
        start_date: startDate,
        end_date: endDate,
        metadata: {
          ...data.add_on_metadata,
          currency_code: data.currency_code,
          customer_field_responses: data.customer_field_responses,
          booking_addon_source: true,
          product_id: 'product_add_ons_main',
          order_line_item_id: createdLineItem.id, // Reference back to order line item
          order_item_id: orderItemId, // Reference back to order item (if found)
        },
      },
    });

    if (!conciergeResult || !conciergeResult.success) {
      throw new Error(`Failed to create concierge order item`);
    }

    console.log("✅ Step 2 completed: Concierge order item created:", conciergeResult.concierge_order_item.id);
    console.log("🔗 Data linking summary:");
    console.log(`   - order_line_item.id: ${createdLineItem.id}`);
    console.log(`   - order_item.id: ${orderItemId || 'not found'}`);
    console.log(`   - concierge_order_item.line_item_id: ${createdLineItem.id}`);
    console.log(`   - concierge_order_item.item_id: ${orderItemId || 'null'}`);

    return res.status(201).json({
      success: true,
      message: "Booking add-on created successfully with proper data linking",
      concierge_order_item: conciergeResult.concierge_order_item,
      order_line_item: createdLineItem,
      order_item_id: orderItemId,
      concierge_order_id: conciergeOrderId,
      order_id: actualOrderId,
      data_linking: {
        order_line_item_id: createdLineItem.id,
        order_item_id: orderItemId,
        concierge_order_item_id: conciergeResult.concierge_order_item.id,
        properly_linked: !!orderItemId,
      },
    });

  } catch (error) {
    console.error("❌ Error creating booking add-on:", error);
    return res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to create booking add-on",
    });
  }
};
